/**
 * @fileoverview Encrypted users routes
 * Handles CRUD operations for encrypted user data
 */

import {
  handleCreateUser,
  handleUpdateUser,
  handleDeleteUser,
  handleGetAllUsers,
  handleGetUserById
} from '../controllers/encrypted-user-controller';

import { createRouter } from '@/utils/router';

export const encryptedUserRouter = createRouter((router) => {
  // Create a new user
  router.post('/', handleCreateUser);

  // Get all users
  router.get('/', handleGetAllUsers);

  // Get user by ID
  router.get('/:id', handleGetUserById);

  // Update user
  router.put('/:id', handleUpdateUser);

  // Delete user
  router.delete('/:id', handleDeleteUser);
});
