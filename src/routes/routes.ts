/**
 * @fileoverview Main application router configuration
 * Sets up base routes and mounts versioned API routes
 */

import apiV1Router from './v1/api-routes';

import type { Router } from 'express';

import { handleHealthCheck } from '@/controllers/common/health-controller';
import { handleWelcome } from '@/controllers/common/welcome-controller';
import { createRouter } from '@/utils/router';

/**
 * Route Paths
 * Centralized route path constants for better maintainability
 */
export const RoutePaths = {
  ROOT: '/',
  HEALTH: '/health',
  API_V1: '/api/v1'
} as const;

/**
 * Creates and configures the main application router
 * - Root route (/) for welcome message
 * - Health check endpoint (/health)
 * - API v1 routes (/api/v1/*)
 */
export default createRouter((router: Router) => {
  // System routes
  router.get(RoutePaths.ROOT, handleWelcome);
  router.get(RoutePaths.HEALTH, handleHealthCheck);

  // API routes
  router.use(RoutePaths.API_V1, apiV1Router);
});
