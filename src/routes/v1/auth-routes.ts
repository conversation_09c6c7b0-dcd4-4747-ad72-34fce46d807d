import type { Router } from 'express';

import {
  handleLogin,
  handleVerifyOtp,
  handleCreateAccount
} from '@/controllers/v1/auth-controller';
import { createRouter } from '@/utils/router';

const authRouter = createRouter((router: Router) => {
  router.get('/login', handleLogin);
  router.get('/create-account', handleCreateAccount);
  router.get('/verify-otp', handleVerifyOtp);
});

export default authRouter;
