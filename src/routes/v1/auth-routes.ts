import type { Router } from 'express';

import {
  handleLogin,
  handleVerifyOtp,
  handleCreateAccount,
  handleSendOtp,
  handleResetPassword
} from '@/controllers/v1/auth-controller';
import { createRouter } from '@/utils/router';

const authRouter = createRouter((router: Router) => {
  router.post('/login', handleLogin);
  router.post('/register', handleCreateAccount);
  router.post('/verify-otp', handleVerifyOtp);
  router.post('/send-otp', handleSendOtp);
  router.post('/reset-password', handleResetPassword);
});

export default authRouter;
