/**
 * @fileoverview V1 API routes configuration
 * Mounts all v1 API endpoints and their respective routers
 */

import { encryptedUserRouter } from '../encrypted-user-routes';
import authRouter from './auth-routes';
import patientPortalRouter from './patient-portal-routes';

import type { Router } from 'express';

import { createRouter } from '@/utils/router';

/**
 * V1 API Route Paths
 * Centralized route path constants for V1 API endpoints
 */
export const V1RoutePaths = {
  AUTH: '/auth',
  PATIENT: '/patient',
  ENCRYPTED_USERS: '/encrypted-users'
} as const;

/**
 * Creates and configures V1 API router
 * Mounts all V1 API endpoints:
 * - Authentication routes (/auth/*)
 * - Patient portal routes (/patient/*)
 * - Encrypted users routes (/encrypted-users/*)
 */
const apiV1Router = createRouter((router: Router) => {
  // Authentication routes
  router.use(V1RoutePaths.AUTH, authRouter);

  // Patient portal routes
  router.use(V1RoutePaths.PATIENT, patientPortalRouter);

  // Encrypted users routes
  router.use(V1RoutePaths.ENCRYPTED_USERS, encryptedUserRouter);
});

export default apiV1Router;
