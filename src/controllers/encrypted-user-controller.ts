/**
 * @fileoverview Encrypted user controller
 * Handles CRUD operations for users with encrypted data
 */

import { EncryptedUserService } from '../services/encrypted-user-service';
import {
  getUserSchema,
  createUserSchema,
  updateUserSchema,
  deleteUserSchema
} from '../validators';

import type EncryptedUser from '../@types/encrypted-user';

import { BackendError } from '@/utils/errors';
import { createHandler } from '@/utils/handler';

const userService = new EncryptedUserService();

// Convert Date to ISO string or use fallback
const formatDate = (date: Date | null): string => {
  return date?.toISOString() ?? new Date().toISOString();
};

// Format user for response
const formatUserResponse = (
  user: EncryptedUser.DecryptedUser
): EncryptedUser.UserResponse => ({
  id: user.id,
  email: user.email,
  firstName: user.firstName,
  lastName: user.lastName,
  createdAt: formatDate(user.createdAt),
  updatedAt: formatDate(user.updatedAt)
});

// Handlers
export const handleCreateUser = createHandler(
  createUserSchema,
  async (req, res) => {
    try {
      const user = await userService.createUser(req.body);

      res.status(201).json({
        success: true,
        data: formatUserResponse(user)
      });
    } catch {
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to create user',
        context: 'body',
        statusCode: 500
      });
    }
  }
);

export const handleGetAllUsers = createHandler(async (_req, res) => {
  try {
    const users = await userService.getAllUsers();

    res.json({
      success: true,
      data: users.map(formatUserResponse)
    });
  } catch {
    throw new BackendError('INTERNAL_ERROR', {
      message: 'Failed to retrieve users',
      context: 'query',
      statusCode: 500
    });
  }
});

export const handleGetUserById = createHandler(
  getUserSchema,
  async (req, res) => {
    try {
      const user = await userService.getUserById(parseInt(req.params.id));

      if (!user) {
        throw new BackendError('NOT_FOUND', {
          message: 'User not found',
          context: 'params',
          statusCode: 404
        });
      }

      res.json({
        success: true,
        data: formatUserResponse(user)
      });
    } catch (error) {
      if (error instanceof BackendError) throw error;
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to retrieve user',
        context: 'query',
        statusCode: 500
      });
    }
  }
);

export const handleUpdateUser = createHandler(
  updateUserSchema,
  async (req, res) => {
    try {
      const user = await userService.updateUser(
        parseInt(req.params.id),
        req.body
      );

      if (!user) {
        throw new BackendError('NOT_FOUND', {
          message: 'User not found',
          context: 'params',
          statusCode: 404
        });
      }

      res.json({
        success: true,
        data: formatUserResponse(user)
      });
    } catch (error) {
      if (error instanceof BackendError) throw error;
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to update user',
        context: 'body',
        statusCode: 500
      });
    }
  }
);

export const handleDeleteUser = createHandler(
  deleteUserSchema,
  async (req, res) => {
    try {
      await userService.deleteUser(parseInt(req.params.id));
      res.status(204).send();
    } catch {
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to delete user',
        context: 'params',
        statusCode: 500
      });
    }
  }
);
