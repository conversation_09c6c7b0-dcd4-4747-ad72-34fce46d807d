import { z } from 'zod';

import { createHandler } from '@/utils/handler';

export const handleCheckEmailExits = createHandler(
  {
    request: {
      query: z.object({
        email: z.string().email()
      })
    }
  },
  async (req, res) => {
    res.send('Login');
  }
);

// Example without schema
export const handleCreateAccount = createHandler(async (req, res) => {
  res.send('Login');
});

export const handleLogin = createHandler(async (req, res) => {
  res.send('Login');
});

export const handleResendOtp = createHandler(async (req, res) => {
  res.send('Login');
});

export const handleVerifyOtp = createHandler(async (req, res) => {
  res.send('Login');
});

export const handleForgotPassword = createHandler(async (req, res) => {
  res.send('Login');
});

export const handleResetPassword = createHandler(async (req, res) => {
  res.send('Login');
});
