/**
 * @fileoverview Tenant management controller
 * Handles tenant configuration, domain settings, and tenant details
 */

import { z } from 'zod';

import { BackendError } from '@/utils/errors';
import { createHandler } from '@/utils/handler';

// Common tenant schema fields
const tenantBaseSchema = {
  id: z.string().uuid(),
  name: z.string(),
  domain: z
    .string()
    .regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/),
  status: z.enum(['active', 'inactive', 'suspended']),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime()
};

// Domain configuration schema
const tenantDomainConfigSchema = {
  request: {
    query: z.object({
      domain: z.string().min(1, 'Domain name is required')
    })
  },
  response: {
    body: z.object({
      ...tenantBaseSchema,
      domainSettings: z.object({
        customDomain: z.string().optional(),
        sslEnabled: z.boolean(),
        redirectRules: z
          .array(
            z.object({
              from: z.string(),
              to: z.string(),
              type: z.enum(['permanent', 'temporary'])
            })
          )
          .optional()
      })
    })
  }
} as const;

// Tenant configuration schema
const tenantConfigSchema = {
  request: {
    params: z.object({
      tenantId: z.string().uuid('Invalid tenant ID')
    })
  },
  response: {
    body: z.object({
      ...tenantBaseSchema,
      config: z.object({
        theme: z.object({
          primaryColor: z.string(),
          secondaryColor: z.string(),
          logo: z.string().url()
        }),
        features: z.object({
          appointments: z.boolean(),
          telemedicine: z.boolean(),
          payments: z.boolean()
        }),
        integrations: z.record(z.string(), z.boolean())
      })
    })
  }
} as const;

// Tenant details schema
const tenantDetailsSchema = {
  request: {
    params: z.object({
      tenantId: z.string().uuid('Invalid tenant ID')
    })
  },
  response: {
    body: z.object({
      ...tenantBaseSchema,
      details: z.object({
        address: z.object({
          street: z.string(),
          city: z.string(),
          state: z.string(),
          country: z.string(),
          postalCode: z.string()
        }),
        contact: z.object({
          email: z.string().email(),
          phone: z.string(),
          website: z.string().url().optional()
        }),
        businessHours: z.array(
          z.object({
            day: z.enum([
              'monday',
              'tuesday',
              'wednesday',
              'thursday',
              'friday',
              'saturday',
              'sunday'
            ]),
            open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
            close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
          })
        )
      })
    })
  }
} as const;

/**
 * Get tenant configuration by domain name
 * Used for initial tenant resolution based on accessed domain
 */
export const handleGetTenantByDomainConfig = createHandler(
  tenantDomainConfigSchema,
  async (req, res) => {
    const { domain } = req.query;

    // TODO: Replace with actual database query
    const tenant = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Example Healthcare',
      domain: domain,
      status: 'active' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      domainSettings: {
        customDomain: 'health.example.com',
        sslEnabled: true,
        redirectRules: [
          {
            from: 'old.example.com',
            to: 'health.example.com',
            type: 'permanent' as const
          }
        ]
      }
    };

    if (!tenant) {
      throw new BackendError('NOT_FOUND', {
        message: 'Tenant not found for this domain',
        context: 'query',
        statusCode: 404
      });
    }

    res.json(tenant);
  }
);

/**
 * Get tenant configuration
 * Returns theme, feature flags, and integration settings
 */
export const handleGetTenantConfig = createHandler(
  tenantConfigSchema,
  async (req, res) => {
    const { tenantId } = req.params;

    // TODO: Replace with actual database query
    const tenant = {
      id: tenantId,
      name: 'Example Healthcare',
      domain: 'health.example.com',
      status: 'active' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      config: {
        theme: {
          primaryColor: '#0066cc',
          secondaryColor: '#ff9900',
          logo: 'https://example.com/logo.png'
        },
        features: {
          appointments: true,
          telemedicine: true,
          payments: true
        },
        integrations: {
          'electronic-health-records': true,
          'payment-gateway': true,
          'video-conferencing': true
        }
      }
    };

    if (!tenant) {
      throw new BackendError('NOT_FOUND', {
        message: 'Tenant configuration not found',
        context: 'params',
        statusCode: 404
      });
    }

    res.json(tenant);
  }
);

/**
 * Get tenant details
 * Returns detailed tenant information including contact and business hours
 */
export const handleGetTenantDetails = createHandler(
  tenantDetailsSchema,
  async (req, res) => {
    const { tenantId } = req.params;

    // TODO: Replace with actual database query
    const tenant = {
      id: tenantId,
      name: 'Example Healthcare',
      domain: 'health.example.com',
      status: 'active' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      details: {
        address: {
          street: '123 Healthcare Ave',
          city: 'Medical City',
          state: 'Health State',
          country: 'United States',
          postalCode: '12345'
        },
        contact: {
          email: '<EMAIL>',
          phone: '+**********',
          website: 'https://example.com'
        },
        businessHours: [
          {
            day: 'monday' as const,
            open: '09:00',
            close: '17:00'
          },
          {
            day: 'tuesday' as const,
            open: '09:00',
            close: '17:00'
          }
        ]
      }
    };

    if (!tenant) {
      throw new BackendError('NOT_FOUND', {
        message: 'Tenant details not found',
        context: 'params',
        statusCode: 404
      });
    }

    res.json(tenant);
  }
);
