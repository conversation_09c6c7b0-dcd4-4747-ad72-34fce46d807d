/**
 * @fileoverview Authentication controller
 * Handles user authentication, account creation, and OTP verification
 */

import { z } from 'zod';

import { create<PERSON><PERSON><PERSON> } from '@/utils/handler';
import { authService } from '@/services/auth-service';

// Login schemas
const loginSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format'),
      password: z.string().min(8, 'Password must be at least 8 characters')
    })
  },
  response: {
    body: z.object({
      token: z.string(),
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string()
      })
    })
  }
} as const;

// Account creation schemas
const createAccountSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format'),
      password: z.string().min(8, 'Password must be at least 8 characters'),
      name: z.string().min(2, 'Name must be at least 2 characters'),
      phone: z
        .string()
        .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
    })
  },
  response: {
    body: z.object({
      message: z.string(),
      userId: z.string()
    })
  }
} as const;

// OTP verification schemas
const verifyOtpSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format'),
      otp: z.string().length(6, 'OTP must be 6 digits')
    })
  },
  response: {
    body: z.object({
      token: z.string(),
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string(),
        verified: z.boolean()
      })
    })
  }
} as const;

// Send OTP schemas
const sendOtpSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format')
    })
  },
  response: {
    body: z.object({
      message: z.string()
    })
  }
} as const;

// Reset password schemas
const resetPasswordSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format'),
      otp: z.string().length(6, 'OTP must be 6 digits'),
      newPassword: z.string().min(8, 'Password must be at least 8 characters')
    })
  },
  response: {
    body: z.object({
      message: z.string()
    })
  }
} as const;

/**
 * Handles user login
 * Validates credentials and returns JWT token if valid
 */
export const handleLogin = createHandler(loginSchema, async (req, res) => {
  const { email, password } = req.body;

  const result = await authService.login({ email, password });

  res.json({
    token: result.token,
    user: {
      id: result.user.userId.toString(),
      email: result.user.email,
      name:
        `${result.user.firstName || ''} ${result.user.lastName || ''}`.trim() ||
        'User'
    }
  });
});

/**
 * Handles new account creation
 * Creates user account and sends verification OTP
 */
export const handleCreateAccount = createHandler(
  createAccountSchema,
  async (req, res) => {
    const { email, password, name, phone } = req.body;

    // Split name into first and last name
    const nameParts = name.split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    const result = await authService.register({
      email,
      password,
      firstName,
      lastName,
      phone
    });

    res.status(201).json({
      message: result.message,
      userId: result.userId.toString()
    });
  }
);

/**
 * Handles OTP verification
 * Verifies OTP and activates user account
 */
export const handleVerifyOtp = createHandler(
  verifyOtpSchema,
  async (req, res) => {
    const { email, otp } = req.body;

    const result = await authService.verifyOtp({ email, otp });

    res.json({
      token: result.token,
      user: {
        id: result.user.userId.toString(),
        email: result.user.email,
        name:
          `${result.user.firstName || ''} ${result.user.lastName || ''}`.trim() ||
          'User',
        verified: result.user.emailVerified || false
      }
    });
  }
);

/**
 * Handles sending OTP for password reset
 */
export const handleSendOtp = createHandler(sendOtpSchema, async (req, res) => {
  const { email } = req.body;

  const result = await authService.sendOtp(email);

  res.json({
    message: result.message
  });
});

/**
 * Handles password reset with OTP
 */
export const handleResetPassword = createHandler(
  resetPasswordSchema,
  async (req, res) => {
    const { email, otp, newPassword } = req.body;

    const result = await authService.resetPassword({
      email,
      otp,
      newPassword
    });

    res.json({
      message: result.message
    });
  }
);
