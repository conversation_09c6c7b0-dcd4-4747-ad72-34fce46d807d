-- CREATE TYPE "public"."enum_user_files_type" AS ENUM('audio', 'video', 'file', 'image');--> statement-breakpoint
-- CREATE TYPE "public"."enum_notification_reminder_notification_for_type" AS ENUM('schedule_pending', 'intake_questionnaire', 'follow_up');--> statement-breakpoint
-- CREATE TYPE "public"."enum_notification_reminder_notification_status" AS ENUM('pending', 'sent', 'failed');--> statement-breakpoint
-- ALTER TYPE "public"."enum_payment_details_payment_gateway" ADD VALUE 'no_payment';--> statement-breakpoint
-- ALTER TYPE "public"."enum_payment_details_payment_gateway" ADD VALUE 'nmi';--> statement-breakpoint
-- ALTER TYPE "public"."enum_prescription_preference_preference" ADD VALUE 'wellsync-pharmacy-hub';--> statement-breakpoint
-- ALTER TYPE "public"."enum_service_payment_mapping_payment_type" ADD VALUE 'no_payment';--> statement-breakpoint
-- ALTER TYPE "public"."enum_telehealth_service_order_status" ADD VALUE 'user_consent_pending';--> statement-breakpoint
-- ALTER TYPE "public"."enum_telehealth_service_questions_question_for" ADD VALUE 'provider';--> statement-breakpoint
-- ALTER TYPE "public"."enum_transactions_payment_method_type" ADD VALUE 'RECURLY';--> statement-breakpoint
-- ALTER TYPE "public"."enum_transactions_payment_method_type" ADD VALUE 'AUTHORISED_NET';--> statement-breakpoint
-- ALTER TYPE "public"."enum_transactions_payment_method_type" ADD VALUE 'NMI';--> statement-breakpoint
-- ALTER TYPE "public"."enum_transactions_payment_status" ADD VALUE 'RECURLY';--> statement-breakpoint
-- ALTER TYPE "public"."enum_transactions_payment_status" ADD VALUE 'AUTHORISED_NET';--> statement-breakpoint
-- CREATE TABLE "consult_note_templates" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"user_id" integer NOT NULL,
-- 	"title" varchar(150) DEFAULT '',
-- 	"icd_code" jsonb DEFAULT '[]'::jsonb,
-- 	"cpt_code" jsonb DEFAULT '[]'::jsonb,
-- 	"subjective" text DEFAULT '',
-- 	"objective" text DEFAULT '',
-- 	"assessment" text DEFAULT '',
-- 	"plan" text DEFAULT '',
-- 	"created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	"updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	"deleted_at" timestamp with time zone
-- );
-- --> statement-breakpoint
-- CREATE TABLE "encrypted_users" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"email" varchar(255) NOT NULL,
-- 	"first_name" text,
-- 	"last_name" text,
-- 	"created_at" timestamp with time zone DEFAULT now(),
-- 	"updated_at" timestamp with time zone DEFAULT now(),
-- 	CONSTRAINT "encrypted_users_email_unique" UNIQUE("email")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "tenant_files" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"tenant_id" integer NOT NULL,
-- 	"service_id" integer,
-- 	"key" varchar(255),
-- 	"disclaimer_file" json,
-- 	"created_at" timestamp with time zone,
-- 	"updated_at" timestamp with time zone,
-- 	"deleted" boolean,
-- 	"deleted_at" timestamp with time zone
-- );
-- --> statement-breakpoint
-- CREATE TABLE "user_licenses" (
-- 	"user_license_id" serial PRIMARY KEY NOT NULL,
-- 	"user_id" integer NOT NULL,
-- 	"number" varchar(255) NOT NULL,
-- 	"state_id" integer NOT NULL,
-- 	"state" varchar(255) NOT NULL,
-- 	"end_date" timestamp with time zone NOT NULL,
-- 	"created_at" timestamp with time zone,
-- 	"updated_at" timestamp with time zone,
-- 	"deleted" boolean,
-- 	"deleted_at" timestamp with time zone
-- );
-- --> statement-breakpoint
-- CREATE TABLE "notification_reminder" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"order_id" integer,
-- 	"user_id" integer,
-- 	"last_reminder_date" timestamp with time zone,
-- 	"reminder_count" integer DEFAULT 0 NOT NULL,
-- 	"notification_for_type" "enum_notification_reminder_notification_for_type" DEFAULT 'intake_questionnaire' NOT NULL,
-- 	"notification_status" "enum_notification_reminder_notification_status" DEFAULT 'pending' NOT NULL,
-- 	"error_message" text,
-- 	"order_guid" varchar(50),
-- 	"user_guid" varchar(50),
-- 	"created_at" timestamp with time zone,
-- 	"updated_at" timestamp with time zone,
-- 	"deleted_at" timestamp with time zone,
-- 	"deleted" boolean DEFAULT false
-- );
-- --> statement-breakpoint
-- CREATE TABLE "user_external_id_mapping" (
-- 	"user_external_id_mapping_id" serial PRIMARY KEY NOT NULL,
-- 	"user_id" integer NOT NULL,
-- 	"external_id" varchar(255) NOT NULL,
-- 	"external_id_source" varchar(255) NOT NULL,
-- 	"created_at" timestamp with time zone,
-- 	"updated_at" timestamp with time zone,
-- 	"deleted" boolean DEFAULT false
-- );
-- --> statement-breakpoint
-- CREATE TABLE "telehealth_service_category" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"name" varchar(200) NOT NULL,
-- 	"slug" varchar(200) NOT NULL,
-- 	"title" varchar(200),
-- 	"subtitle" varchar(500),
-- 	"icon" varchar(500) NOT NULL,
-- 	"filter_display" boolean DEFAULT true NOT NULL,
-- 	"service_category_guid" uuid DEFAULT uuid_generate_v4() NOT NULL,
-- 	"tennant_id" integer,
-- 	"created_at" timestamp with time zone NOT NULL,
-- 	"updated_at" timestamp with time zone NOT NULL,
-- 	"deleted_at" timestamp with time zone,
-- 	"deleted" boolean DEFAULT false
-- );
-- --> statement-breakpoint
-- DROP VIEW "public"."dashboard_tennant_list";--> statement-breakpoint
-- ALTER TABLE "mms_prescriptions" RENAME COLUMN "flag340b" TO "flag340B";--> statement-breakpoint
-- ALTER TABLE "chat_files" DROP CONSTRAINT "chat_files_room_id_chat_rooms_id_fk";
-- --> statement-breakpoint
-- ALTER TABLE "user_subscription_billing" ALTER COLUMN "total_price" SET DEFAULT '0';--> statement-breakpoint
-- ALTER TABLE "telehealth_service_question_answer_dump" ALTER COLUMN "answer" SET DEFAULT 'false';--> statement-breakpoint
-- ALTER TABLE "tennant_config" ALTER COLUMN "theme_color" DROP NOT NULL;--> statement-breakpoint
-- ALTER TABLE "service_action_preference" ALTER COLUMN "service_id" DROP NOT NULL;--> statement-breakpoint
-- ALTER TABLE "service_action_preference" ALTER COLUMN "sms_template" SET DATA TYPE text;--> statement-breakpoint
-- ALTER TABLE "telehealth_services" ALTER COLUMN "user_consent" SET DATA TYPE text;--> statement-breakpoint
-- ALTER TABLE "transactions" ADD COLUMN "payment_details_id" integer;--> statement-breakpoint
-- ALTER TABLE "subscription_plans" ADD COLUMN "deleted" boolean DEFAULT false;--> statement-breakpoint
-- ALTER TABLE "subscription_plans" ADD COLUMN "deleted_at" timestamp with time zone;--> statement-breakpoint
-- ALTER TABLE "subscription_plans" ADD COLUMN "deleted_by" integer;--> statement-breakpoint
-- ALTER TABLE "subscription_plans" ADD COLUMN "visit_count" integer DEFAULT 1;--> statement-breakpoint
-- ALTER TABLE "consult_order_files" ADD COLUMN "file_key" varchar(255) DEFAULT 'consult_file' NOT NULL;--> statement-breakpoint
-- ALTER TABLE "user_subscription" ADD COLUMN "consult_limit" integer;--> statement-breakpoint
-- ALTER TABLE "user_subscription" ADD COLUMN "consult_count" integer;--> statement-breakpoint
-- ALTER TABLE "tennant_master" ADD COLUMN "type" varchar(255);--> statement-breakpoint
-- ALTER TABLE "telehealth_service_question_answer_dump" ADD COLUMN "answered_by" integer;--> statement-breakpoint
-- ALTER TABLE "users" ADD COLUMN "user_private_key" text;--> statement-breakpoint
-- ALTER TABLE "tennant_config" ADD COLUMN "theme_config" json;--> statement-breakpoint
-- ALTER TABLE "tennant_config" ADD COLUMN "is_external" boolean DEFAULT false;--> statement-breakpoint
-- ALTER TABLE "service_action_preference" ADD COLUMN "email_subject" varchar(255);--> statement-breakpoint
-- ALTER TABLE "service_action_preference" ADD COLUMN "cc_recipient" json DEFAULT '[]'::json;--> statement-breakpoint
-- ALTER TABLE "service_action_preference" ADD COLUMN "bcc_recipient" json DEFAULT '[]'::json;--> statement-breakpoint
-- ALTER TABLE "telehealth_service_master" ADD COLUMN "categories" integer[] DEFAULT '{}';--> statement-breakpoint
-- ALTER TABLE "telehealth_service_master" ADD COLUMN "filter_display" boolean DEFAULT true NOT NULL;--> statement-breakpoint
-- ALTER TABLE "telehealth_service_master" ADD COLUMN "icons" varchar(500) DEFAULT NULL;--> statement-breakpoint
-- ALTER TABLE "telehealth_service_master" ADD COLUMN "icon" varchar(500) DEFAULT NULL;--> statement-breakpoint
-- ALTER TABLE "telehealth_services" ADD COLUMN "preferred_pharmacy_selection" varchar(255);--> statement-breakpoint
-- ALTER TABLE "telehealth_services" ADD COLUMN "next_followup_days" integer;--> statement-breakpoint
-- ALTER TABLE "user_files" ADD COLUMN "type" "enum_user_files_type";--> statement-breakpoint
-- ALTER TABLE "user_files" ADD COLUMN "file_key" varchar(255);--> statement-breakpoint
-- ALTER TABLE "user_files" ADD COLUMN "tenant_id" integer;--> statement-breakpoint
-- ALTER TABLE "pharmacy_state_service_mapping" ADD COLUMN "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL;--> statement-breakpoint
-- ALTER TABLE "pharmacy_state_service_mapping" ADD COLUMN "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL;--> statement-breakpoint
-- ALTER TABLE "payment_details" ADD COLUMN "site_id" varchar(255) DEFAULT NULL;--> statement-breakpoint
-- ALTER TABLE "consult_note_templates" ADD CONSTRAINT "consult_note_templates_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "tenant_files" ADD CONSTRAINT "tenant_files_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "tenant_files" ADD CONSTRAINT "tenant_files_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "user_licenses" ADD CONSTRAINT "user_licenses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "user_licenses" ADD CONSTRAINT "user_licenses_state_id_fkey" FOREIGN KEY ("state_id") REFERENCES "public"."states"("state_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "notification_reminder" ADD CONSTRAINT "notification_reminder_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "notification_reminder" ADD CONSTRAINT "notification_reminder_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "user_external_id_mapping" ADD CONSTRAINT "user_external_id_mapping_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "telehealth_service_category" ADD CONSTRAINT "telehealth_service_category_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "transactions" ADD CONSTRAINT "transactions_payment_details_id_fkey" FOREIGN KEY ("payment_details_id") REFERENCES "public"."payment_details"("payment_details_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "subscription_plans" ADD CONSTRAINT "subscription_plans_deleted_by_fkey" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "chat_files" ADD CONSTRAINT "chat_files_room_id_ chat_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "telehealth_service_question_answer_dump" ADD CONSTRAINT "telehealth_service_question_answer_dump_answered_by_fkey" FOREIGN KEY ("answered_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "user_files" ADD CONSTRAINT "user_files_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "service_payment_mapping" DROP COLUMN "payment_type";