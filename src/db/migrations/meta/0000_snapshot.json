{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.SequelizeMeta": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.auth_provider": {"name": "auth_provider", "schema": "", "columns": {"auth_id": {"name": "auth_id", "type": "serial", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "json_token_id": {"name": "json_token_id", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "iat": {"name": "iat", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_jti_valid": {"name": "is_jti_valid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"auth_provider_user_id_fkey": {"name": "auth_provider_user_id_fkey", "tableFrom": "auth_provider", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.billing": {"name": "billing", "schema": "", "columns": {"plan_id": {"name": "plan_id", "type": "serial", "primaryKey": false, "notNull": true}, "plan_name": {"name": "plan_name", "type": "text", "primaryKey": false, "notNull": false}, "plan_description": {"name": "plan_description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.contact_us": {"name": "contact_us", "schema": "", "columns": {"contact_us_id": {"name": "contact_us_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "date_requested": {"name": "date_requested", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true, "default": "''"}, "question": {"name": "question", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true, "default": "''"}, "queryText": {"name": "queryText", "type": "<PERSON><PERSON><PERSON>(4000)", "primaryKey": false, "notNull": true, "default": "''"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.consult_notes": {"name": "consult_notes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": true}, "icd_code": {"name": "icd_code", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "cpt_code": {"name": "cpt_code", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "subjective": {"name": "subjective", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "objective": {"name": "objective", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "assessment": {"name": "assessment", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "erm_id": {"name": "erm_id", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": false, "default": "''"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "intervention": {"name": "intervention", "type": "text", "primaryKey": false, "notNull": false}, "outcome": {"name": "outcome", "type": "text", "primaryKey": false, "notNull": false}, "goal": {"name": "goal", "type": "text", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "general_note": {"name": "general_note", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false, "default": "NULL"}}, "indexes": {}, "foreignKeys": {"consult_notes_order_id_fkey": {"name": "consult_notes_order_id_fkey", "tableFrom": "consult_notes", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_notes_order_guid_fkey": {"name": "consult_notes_order_guid_fkey", "tableFrom": "consult_notes", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_guid"], "columnsTo": ["order_guid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.affiliate_pharmacy": {"name": "affiliate_pharmacy", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "pharmacy_name": {"name": "pharmacy_name", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false, "default": "''"}, "street_one": {"name": "street_one", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "street_two": {"name": "street_two", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(75)", "primaryKey": false, "notNull": false, "default": "''"}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "''"}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "fax_number": {"name": "fax_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.educational_videos": {"name": "educational_videos", "schema": "", "columns": {"video_id": {"name": "video_id", "type": "serial", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "archive_id": {"name": "archive_id", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "audio_stream_screenshot": {"name": "audio_stream_screenshot", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"educational_videos_created_by_fkey": {"name": "educational_videos_created_by_fkey", "tableFrom": "educational_videos", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.encounters_values": {"name": "encounters_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"encounters_values_summary_id_fkey": {"name": "encounters_values_summary_id_fkey", "tableFrom": "encounters_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.requests": {"name": "requests", "schema": "", "columns": {"request_id": {"name": "request_id", "type": "serial", "primaryKey": true, "notNull": true}, "requestor_id": {"name": "requestor_id", "type": "integer", "primaryKey": false, "notNull": true}, "requestee_id": {"name": "requestee_id", "type": "integer", "primaryKey": false, "notNull": true}, "object_id": {"name": "object_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "enum_requests_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'open'"}, "message": {"name": "message", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": false}, "patient_history": {"name": "patient_history", "type": "text", "primaryKey": false, "notNull": false}, "requestor_read_status": {"name": "requestor_read_status", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "requestee_read_status": {"name": "requestee_read_status", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "entity_id": {"name": "entity_id", "type": "integer", "primaryKey": false, "notNull": false}, "add_practice_group_doctors": {"name": "add_practice_group_doctors", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "NULL"}, "release_medical": {"name": "release_medical", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "rescheduled": {"name": "rescheduled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"requests_object_id_fkey": {"name": "requests_object_id_fkey", "tableFrom": "requests", "tableTo": "request_objects", "schemaTo": "public", "columnsFrom": ["object_id"], "columnsTo": ["object_id"], "onDelete": "no action", "onUpdate": "no action"}, "requests_order_guid_fkey": {"name": "requests_order_guid_fkey", "tableFrom": "requests", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_guid"], "columnsTo": ["order_guid"], "onDelete": "no action", "onUpdate": "no action"}, "requests_requestee_id_fkey": {"name": "requests_requestee_id_fkey", "tableFrom": "requests", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["requestee_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "requests_requestor_id_fkey": {"name": "requests_requestor_id_fkey", "tableFrom": "requests", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["requestor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.encryption_keys": {"name": "encryption_keys", "schema": "", "columns": {"encryption_key_id": {"name": "encryption_key_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "device_id": {"name": "device_id", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "registration_id": {"name": "registration_id", "type": "integer", "primaryKey": false, "notNull": true}, "identity_key": {"name": "identity_key", "type": "text", "primaryKey": false, "notNull": false}, "signed_pre_key": {"name": "signed_pre_key", "type": "text", "primaryKey": false, "notNull": false}, "pre_key": {"name": "pre_key", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"encryption_keys_user_id_fkey": {"name": "encryption_keys_user_id_fkey", "tableFrom": "encryption_keys", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.health_summaries_schedule": {"name": "health_summaries_schedule", "schema": "", "columns": {"schedule_id": {"name": "schedule_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "next_run_date": {"name": "next_run_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"health_summaries_schedule_user_id_fkey": {"name": "health_summaries_schedule_user_id_fkey", "tableFrom": "health_summaries_schedule", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.precanned_messages": {"name": "precanned_messages", "schema": "", "columns": {"message_id": {"name": "message_id", "type": "serial", "primaryKey": true, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.feed": {"name": "feed", "schema": "", "columns": {"feed_id": {"name": "feed_id", "type": "serial", "primaryKey": true, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"feed_created_by_fkey": {"name": "feed_created_by_fkey", "tableFrom": "feed", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "feed_user_id_fkey": {"name": "feed_user_id_fkey", "tableFrom": "feed", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.forms": {"name": "forms", "schema": "", "columns": {"form_id": {"name": "form_id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.promo_codes": {"name": "promo_codes", "schema": "", "columns": {"code_id": {"name": "code_id", "type": "serial", "primaryKey": true, "notNull": true}, "code_type": {"name": "code_type", "type": "enum_promo_codes_code_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "lab_id": {"name": "lab_id", "type": "integer", "primaryKey": false, "notNull": false}, "discount_type": {"name": "discount_type", "type": "enum_promo_codes_discount_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "discount_values": {"name": "discount_values", "type": "text", "primaryKey": false, "notNull": false}, "promo_code": {"name": "promo_code", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "usage_type": {"name": "usage_type", "type": "enum_promo_codes_usage_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "integer", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"promo_codes_service_id_fkey": {"name": "promo_codes_service_id_fkey", "tableFrom": "promo_codes", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.drugs": {"name": "drugs", "schema": "", "columns": {"drug_id": {"name": "drug_id", "type": "serial", "primaryKey": true, "notNull": true}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": false}, "drug_full_name": {"name": "drug_full_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "tier": {"name": "tier", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "quantity": {"name": "quantity", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"drugs_category_id_fkey": {"name": "drugs_category_id_fkey", "tableFrom": "drugs", "tableTo": "drugs_category", "schemaTo": "public", "columnsFrom": ["category_id"], "columnsTo": ["category_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"invitation_id": {"name": "invitation_id", "type": "serial", "primaryKey": true, "notNull": true}, "invitor_id": {"name": "invitor_id", "type": "integer", "primaryKey": false, "notNull": true}, "install_type": {"name": "install_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "accepted": {"name": "accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "add_practice_group_doctors": {"name": "add_practice_group_doctors", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "expiry": {"name": "expiry", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"invitations_invitor_id_fkey": {"name": "invitations_invitor_id_fkey", "tableFrom": "invitations", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["invitor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.login_requests": {"name": "login_requests", "schema": "", "columns": {"login_request_id": {"name": "login_request_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "bad_request": {"name": "bad_request", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"login_requests_user_id_fkey": {"name": "login_requests_user_id_fkey", "tableFrom": "login_requests", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.favourite_drugs": {"name": "favourite_drugs", "schema": "", "columns": {"drug_id": {"name": "drug_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "drug_name": {"name": "drug_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "brand": {"name": "brand", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "form": {"name": "form", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "dosage": {"name": "dosage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refill_quantity": {"name": "refill_quantity", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "favourite": {"name": "favourite", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "direction_quantity": {"name": "direction_quantity", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "direction_one": {"name": "direction_one", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "direction_two": {"name": "direction_two", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tennant_id": {"name": "tennant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "pharmacy_name": {"name": "pharmacy_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "quantity_unit": {"name": "quantity_unit", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "erx_product_id": {"name": "erx_product_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "comments": {"name": "comments", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"favourite_drugs_user_id_fkey": {"name": "favourite_drugs_user_id_fkey", "tableFrom": "favourite_drugs", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.micromerchant_users": {"name": "micromerchant_users", "schema": "", "columns": {"mm_user_id": {"name": "mm_user_id", "type": "serial", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.procedure_codes": {"name": "procedure_codes", "schema": "", "columns": {"procedure_code_id": {"name": "procedure_code_id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.immunizations_values": {"name": "immunizations_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"immunizations_values_summary_id_fkey": {"name": "immunizations_values_summary_id_fkey", "tableFrom": "immunizations_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.medications_values": {"name": "medications_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"medications_values_summary_id_fkey": {"name": "medications_values_summary_id_fkey", "tableFrom": "medications_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.mms_patients": {"name": "mms_patients", "schema": "", "columns": {"patient_id": {"name": "patient_id", "type": "serial", "primaryKey": true, "notNull": true}, "patient_no": {"name": "patient_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "pharmacy_id": {"name": "pharmacy_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "payment_preference": {"name": "payment_preference", "type": "text", "primaryKey": false, "notNull": false}, "messaging_settings": {"name": "messaging_settings", "type": "text", "primaryKey": false, "notNull": false}, "diagnostics": {"name": "diagnostics", "type": "text", "primaryKey": false, "notNull": false}, "allergies": {"name": "allergies", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "insurances": {"name": "insurances", "type": "text", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": false}, "is_smoker": {"name": "is_smoker", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "marital_status": {"name": "marital_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "weight": {"name": "weight", "type": "numeric(10, 3)", "primaryKey": false, "notNull": false}, "is_pregnant": {"name": "is_pregnant", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "height": {"name": "height", "type": "numeric(10, 3)", "primaryKey": false, "notNull": false}, "medical_record_number": {"name": "medical_record_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "species_type": {"name": "species_type", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "dea_restriction_code": {"name": "dea_restriction_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "family_email": {"name": "family_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "patient_remark": {"name": "patient_remark", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "patient_short_remark": {"name": "patient_short_remark", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "family_remark": {"name": "family_remark", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "race": {"name": "race", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "work_phone": {"name": "work_phone", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "mobile": {"name": "mobile", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "chart_no": {"name": "chart_no", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "ez_cap": {"name": "ez_cap", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "discount_code": {"name": "discount_code", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "short_sode": {"name": "short_sode", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "price_code_brand": {"name": "price_code_brand", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "price_code_generic": {"name": "price_code_generic", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "charge_account": {"name": "charge_account", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "print_drug_counselling": {"name": "print_drug_counselling", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "preferred_delivery_method": {"name": "preferred_delivery_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "driver_license_number": {"name": "driver_license_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "hippa_signature": {"name": "hippa_signature", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "driver_license_expiry": {"name": "driver_license_expiry", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"mms_patients_pharmacy_id_fkey": {"name": "mms_patients_pharmacy_id_fkey", "tableFrom": "mms_patients", "tableTo": "pharmacies", "schemaTo": "public", "columnsFrom": ["pharmacy_id"], "columnsTo": ["pharmacy_id"], "onDelete": "no action", "onUpdate": "no action"}, "mms_patients_user_id_fkey": {"name": "mms_patients_user_id_fkey", "tableFrom": "mms_patients", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.mms_request_payload": {"name": "mms_request_payload", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "rxno": {"name": "rxno", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "pharmacy_system": {"name": "pharmacy_system", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "pharmacy_token": {"name": "pharmacy_token", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.mms_prescriptions": {"name": "mms_prescriptions", "schema": "", "columns": {"prescription_id": {"name": "prescription_id", "type": "serial", "primaryKey": true, "notNull": true}, "patient_id": {"name": "patient_id", "type": "integer", "primaryKey": false, "notNull": true}, "rx_no": {"name": "rx_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "auth_refills": {"name": "auth_refills", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "drug_info": {"name": "drug_info", "type": "text", "primaryKey": false, "notNull": false}, "qty_ordered": {"name": "qty_ordered", "type": "numeric", "primaryKey": false, "notNull": false}, "date_ordered": {"name": "date_ordered", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_expires": {"name": "date_expires", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "discontinued": {"name": "discontinued", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sig": {"name": "sig", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "prescriber": {"name": "prescriber", "type": "text", "primaryKey": false, "notNull": false}, "diagnostics": {"name": "diagnostics", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "auth": {"name": "auth", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "source_pms": {"name": "source_pms", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "refill_no": {"name": "refill_no", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "pharmacy_id": {"name": "pharmacy_id", "type": "integer", "primaryKey": false, "notNull": false}, "date_filled": {"name": "date_filled", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "discontinue_reason": {"name": "discontinue_reason", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "date_discontinued": {"name": "date_discontinued", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "days_supplied": {"name": "days_supplied", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "rx_serial_no": {"name": "rx_serial_no", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "bill_type": {"name": "bill_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "bill_as": {"name": "bill_as", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "qty_dispensed": {"name": "qty_dispensed", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "delivery_method": {"name": "delivery_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "hold_rx": {"name": "hold_rx", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "rph": {"name": "rph", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "date_picked": {"name": "date_picked", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "awp": {"name": "awp", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "time_picked": {"name": "time_picked", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "picked_up": {"name": "picked_up", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "prescription_notes": {"name": "prescription_notes", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "delivery_date": {"name": "delivery_date", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "tracking_url": {"name": "tracking_url", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "shipping_service": {"name": "shipping_service", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "source_script_id": {"name": "source_script_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "flag340b": {"name": "flag340b", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "daw_code": {"name": "daw_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "dispense_as_written": {"name": "dispense_as_written", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "source_script_message": {"name": "source_script_message", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "cost_price": {"name": "cost_price", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "rx_amount": {"name": "rx_amount", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "disp_fee": {"name": "disp_fee", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "dosage_fields": {"name": "dosage_fields", "type": "text", "primaryKey": false, "notNull": false}, "patient_copay": {"name": "patient_copay", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "billed": {"name": "billed", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "primary_insurance": {"name": "primary_insurance", "type": "text", "primaryKey": false, "notNull": false}, "fill_list_indicator": {"name": "fill_list_indicator", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "submission_clar_code": {"name": "submission_clar_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "horizon_graveyard_code": {"name": "horizon_graveyard_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "room_number": {"name": "room_number", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "nursing_home_id": {"name": "nursing_home_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "location_code": {"name": "location_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "facility_code": {"name": "facility_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "nursing_home": {"name": "nursing_home", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "wing_code1": {"name": "wing_code1", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "refering_doctor": {"name": "refering_doctor", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_name": {"name": "xfer_to_pharmacy_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "wing_code2": {"name": "wing_code2", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_address1": {"name": "xfer_to_pharmacy_address1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_address2": {"name": "xfer_to_pharmacy_address2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_city": {"name": "xfer_to_pharmacy_city", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_phone": {"name": "xfer_to_pharmacy_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_npi": {"name": "xfer_to_pharmacy_npi", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_ncpdp": {"name": "xfer_to_pharmacy_ncpdp", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "bill_status": {"name": "bill_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "xfer_to_pharmacy_dea": {"name": "xfer_to_pharmacy_dea", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "bill_status_text": {"name": "bill_status_text", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "workflow_status": {"name": "workflow_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "prescribed_drug": {"name": "prescribed_drug", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "workflow_status_text": {"name": "workflow_status_text", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "claim_authorization_number": {"name": "claim_authorization_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "prior_auth_number": {"name": "prior_auth_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "election_prescription_origin_time": {"name": "election_prescription_origin_time", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"mms_prescriptions_patient_id_fkey": {"name": "mms_prescriptions_patient_id_fkey", "tableFrom": "mms_prescriptions", "tableTo": "mms_patients", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["patient_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.onehealth_lab_orders": {"name": "onehealth_lab_orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "registered_kit_id": {"name": "registered_kit_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "testing_kit_type": {"name": "testing_kit_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "''"}, "total_quantity": {"name": "total_quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "use_prescription_service": {"name": "use_prescription_service", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "interval": {"name": "interval", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "results_needs_revision": {"name": "results_needs_revision", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "lab": {"name": "lab", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "'rucdr'"}, "additional_data": {"name": "additional_data", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "'rucdr'"}, "posted_data": {"name": "posted_data", "type": "text", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "'Ravkoo'"}, "return_mailer": {"name": "return_mailer", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "insurance_enabled": {"name": "insurance_enabled", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "kit_ids": {"name": "kit_ids", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "hcp_id": {"name": "hcp_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "test_code": {"name": "test_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "askOnEntry": {"name": "askOnEntry", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "diagnostic_code": {"name": "diagnostic_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "diagnose_observation_date": {"name": "diagnose_observation_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "lab_ref_id": {"name": "lab_ref_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "status": {"name": "status", "type": "enum_onehealth_lab_orders_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'PENDING'"}, "payment_status": {"name": "payment_status", "type": "enum_onehealth_lab_orders_payment_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'PENDING'"}, "one_health": {"name": "one_health", "type": "text", "primaryKey": false, "notNull": false}, "collection_at": {"name": "collection_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "kit_register_at": {"name": "kit_register_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "service_charges": {"name": "service_charges", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "shipping_amount": {"name": "shipping_amount", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_cost": {"name": "total_cost", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "lab_id": {"name": "lab_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "NULL"}, "payment_transaction": {"name": "payment_transaction", "type": "text", "primaryKey": false, "notNull": false}, "flat_price": {"name": "flat_price", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": false, "default": "''"}, "product_code": {"name": "product_code", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "''"}, "discount": {"name": "discount", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}}, "indexes": {}, "foreignKeys": {"onehealth_lab_orders_user_id_fkey": {"name": "onehealth_lab_orders_user_id_fkey", "tableFrom": "onehealth_lab_orders", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"onehealth_lab_orders_order_guid_key": {"columns": ["order_guid"], "nullsNotDistinct": false, "name": "onehealth_lab_orders_order_guid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.patient_insurances": {"name": "patient_insurances", "schema": "", "columns": {"patient_insurance_id": {"name": "patient_insurance_id", "type": "serial", "primaryKey": true, "notNull": true}, "patient_id": {"name": "patient_id", "type": "integer", "primaryKey": false, "notNull": true}, "insurance_id": {"name": "insurance_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"patient_insurances_patient_id_fkey": {"name": "patient_insurances_patient_id_fkey", "tableFrom": "patient_insurances", "tableTo": "mms_patients", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["patient_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.permission_groups": {"name": "permission_groups", "schema": "", "columns": {"patient_id": {"name": "patient_id", "type": "integer", "primaryKey": false, "notNull": true}, "associated_user_id": {"name": "associated_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "group_id": {"name": "group_id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"permission_groups_associated_user_id_fkey": {"name": "permission_groups_associated_user_id_fkey", "tableFrom": "permission_groups", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["associated_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "permission_groups_patient_id_fkey": {"name": "permission_groups_patient_id_fkey", "tableFrom": "permission_groups", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"permission_groups_patient_id_associated_user_id_key": {"columns": ["patient_id", "associated_user_id"], "nullsNotDistinct": false, "name": "permission_groups_patient_id_associated_user_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_order": {"name": "telehealth_service_order", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "answer_given_by": {"name": "answer_given_by", "type": "integer", "primaryKey": false, "notNull": false}, "provider_id": {"name": "provider_id", "type": "integer", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_telehealth_service_order_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "prescription_delivery": {"name": "prescription_delivery", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "ravkoo_prescription_option": {"name": "ravkoo_prescription_option", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "pharmacy_name": {"name": "pharmacy_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "''"}, "pharmacy_phone": {"name": "pharmacy_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "''"}, "pharmacy_address": {"name": "pharmacy_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "''"}, "service_type": {"name": "service_type", "type": "enum_telehealth_service_order_service_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'SYNC'"}, "release_medical": {"name": "release_medical", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "release_medical_at": {"name": "release_medical_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_cancelled_by_provider": {"name": "is_cancelled_by_provider", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cancelled_at": {"name": "cancelled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "pharmacy_city": {"name": "pharmacy_city", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_state": {"name": "pharmacy_state", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_zip": {"name": "pharmacy_zip", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_fax": {"name": "pharmacy_fax", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "pharmacy_preference": {"name": "pharmacy_preference", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "claim_type": {"name": "claim_type", "type": "enum_telehealth_service_order_claim_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "claim_id": {"name": "claim_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payor_name": {"name": "payor_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "injury_date": {"name": "injury_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "current_medicines": {"name": "current_medicines", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "allergies_to_medicines": {"name": "allergies_to_medicines", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "other_allergies": {"name": "other_allergies", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "doctor_notes": {"name": "doctor_notes", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "abnormal_findings": {"name": "abnormal_findings", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "terms_and_conditions_accepted": {"name": "terms_and_conditions_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cancellation_reason": {"name": "cancellation_reason", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_refill_request": {"name": "is_refill_request", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "external_order_id": {"name": "external_order_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "follow_up": {"name": "follow_up", "type": "integer", "primaryKey": false, "notNull": false}, "visit_type": {"name": "visit_type", "type": "enum_telehealth_service_order_visit_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "completion_reason": {"name": "completion_reason", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "session_type": {"name": "session_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "schedule_type": {"name": "schedule_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "affiliateid": {"name": "affiliateid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dosespot_pharmacy_id": {"name": "dosespot_pharmacy_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "pharmacy_ncpdp_id": {"name": "pharmacy_ncpdp_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "erx_prescription_visitied_at": {"name": "erx_prescription_visitied_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "pharmacy_id": {"name": "pharmacy_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_service_order_answer_given_by_fkey": {"name": "telehealth_service_order_answer_given_by_fkey", "tableFrom": "telehealth_service_order", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["answer_given_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_order_order_id_fkey": {"name": "telehealth_service_order_order_id_fkey", "tableFrom": "telehealth_service_order", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_order_provider_id_fkey": {"name": "telehealth_service_order_provider_id_fkey", "tableFrom": "telehealth_service_order", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["provider_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_order_service_id_fkey": {"name": "telehealth_service_order_service_id_fkey", "tableFrom": "telehealth_service_order", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_order_pharmacy_id_fkey": {"name": "telehealth_service_order_pharmacy_id_fkey", "tableFrom": "telehealth_service_order", "tableTo": "pharmacies", "schemaTo": "public", "columnsFrom": ["pharmacy_id"], "columnsTo": ["pharmacy_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"telehealth_service_order_order_guid_key": {"columns": ["order_guid"], "nullsNotDistinct": false, "name": "telehealth_service_order_order_guid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true}, "schedule_id": {"name": "schedule_id", "type": "integer", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "caller_id": {"name": "caller_id", "type": "integer", "primaryKey": false, "notNull": false}, "callee_id": {"name": "callee_id", "type": "integer", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_orders_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "enum_orders_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "enum_orders_category", "typeSchema": "public", "primaryKey": false, "notNull": false}, "conversation_mode": {"name": "conversation_mode", "type": "text", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric", "primaryKey": false, "notNull": false}, "billed": {"name": "billed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "caller_location": {"name": "caller_location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "callee_location": {"name": "callee_location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "instructions": {"name": "instructions", "type": "text", "primaryKey": false, "notNull": false}, "diagnosis": {"name": "diagnosis", "type": "text", "primaryKey": false, "notNull": false}, "procedure": {"name": "procedure", "type": "text", "primaryKey": false, "notNull": false}, "visit_summary": {"name": "visit_summary", "type": "text", "primaryKey": false, "notNull": false}, "regenerate_visit_summary": {"name": "regenerate_visit_summary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "regenerate_ccd_file": {"name": "regenerate_ccd_file", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_virtual_room": {"name": "is_virtual_room", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "hide_visit_details": {"name": "hide_visit_details", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "audio_stream_screenshot": {"name": "audio_stream_screenshot", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "NULL"}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "host_pass_phrase": {"name": "host_pass_phrase", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "viewer_pass_phrase": {"name": "viewer_pass_phrase", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "channel": {"name": "channel", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"orders_callee_id_fkey": {"name": "orders_callee_id_fkey", "tableFrom": "orders", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["callee_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_caller_id_fkey": {"name": "orders_caller_id_fkey", "tableFrom": "orders", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["caller_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_doctor_id_fkey": {"name": "orders_doctor_id_fkey", "tableFrom": "orders", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_order_guid_fkey": {"name": "orders_order_guid_fkey", "tableFrom": "orders", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_guid"], "columnsTo": ["order_guid"], "onDelete": "no action", "onUpdate": "no action"}, "orders_schedule_id_fkey": {"name": "orders_schedule_id_fkey", "tableFrom": "orders", "tableTo": "schedules", "schemaTo": "public", "columnsFrom": ["schedule_id"], "columnsTo": ["schedule_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_order_id_key": {"columns": ["order_id"], "nullsNotDistinct": false, "name": "orders_order_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.practice_groups": {"name": "practice_groups", "schema": "", "columns": {"practice_group_id": {"name": "practice_group_id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "phones": {"name": "phones", "type": "text", "primaryKey": false, "notNull": false}, "visit_address": {"name": "visit_address", "type": "text", "primaryKey": false, "notNull": false}, "install_type": {"name": "install_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.mobile_number_otp_validator": {"name": "mobile_number_otp_validator", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "otp": {"name": "otp", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.consult_update_details_history": {"name": "consult_update_details_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_order_id": {"name": "service_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "previous_values": {"name": "previous_values", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "updated_values": {"name": "updated_values", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"consult_update_details_history_service_order_id_fkey": {"name": "consult_update_details_history_service_order_id_fkey", "tableFrom": "consult_update_details_history", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_update_details_history_user_id_fkey": {"name": "consult_update_details_history_user_id_fkey", "tableFrom": "consult_update_details_history", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_update_details_history_service_order_id_fkey1": {"name": "consult_update_details_history_service_order_id_fkey1", "tableFrom": "consult_update_details_history", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_update_details_history_service_order_id_fkey2": {"name": "consult_update_details_history_service_order_id_fkey2", "tableFrom": "consult_update_details_history", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_update_details_history_service_order_id_fkey3": {"name": "consult_update_details_history_service_order_id_fkey3", "tableFrom": "consult_update_details_history", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_update_details_history_service_order_id_fkey4": {"name": "consult_update_details_history_service_order_id_fkey4", "tableFrom": "consult_update_details_history", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.provider_license": {"name": "provider_license", "schema": "", "columns": {"provider_license_id": {"name": "provider_license_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "license_state": {"name": "license_state", "type": "integer", "primaryKey": false, "notNull": false}, "license_state_name": {"name": "license_state_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "license_expiration_date": {"name": "license_expiration_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"provider_license_user_id_fkey": {"name": "provider_license_user_id_fkey", "tableFrom": "provider_license", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "provider_license_license_state_fkey": {"name": "provider_license_license_state_fkey", "tableFrom": "provider_license", "tableTo": "states", "schemaTo": "public", "columnsFrom": ["license_state"], "columnsTo": ["state_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.requests_log": {"name": "requests_log", "schema": "", "columns": {"request_log_id": {"name": "request_log_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "install_type": {"name": "install_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "response_http_status": {"name": "response_http_status", "type": "integer", "primaryKey": false, "notNull": true}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"requests_log_user_id_fkey": {"name": "requests_log_user_id_fkey", "tableFrom": "requests_log", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.request_objects": {"name": "request_objects", "schema": "", "columns": {"object_id": {"name": "object_id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.medicines": {"name": "medicines", "schema": "", "columns": {"medicine_id": {"name": "medicine_id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "brand": {"name": "brand", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "form": {"name": "form", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "dosage": {"name": "dosage", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "refill_quantity": {"name": "refill_quantity", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "direction_quantity": {"name": "direction_quantity", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "direction_one": {"name": "direction_one", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "direction_two": {"name": "direction_two", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.medicine_service_pharmacy_mapping": {"name": "medicine_service_pharmacy_mapping", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "medicine_id": {"name": "medicine_id", "type": "integer", "primaryKey": false, "notNull": true}, "pharmacy_id": {"name": "pharmacy_id", "type": "integer", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {"medicine_service_pharmacy_mapping_medicine_id_fkey": {"name": "medicine_service_pharmacy_mapping_medicine_id_fkey", "tableFrom": "medicine_service_pharmacy_mapping", "tableTo": "medicines", "schemaTo": "public", "columnsFrom": ["medicine_id"], "columnsTo": ["medicine_id"], "onDelete": "no action", "onUpdate": "no action"}, "medicine_service_pharmacy_mapping_pharmacy_id_fkey": {"name": "medicine_service_pharmacy_mapping_pharmacy_id_fkey", "tableFrom": "medicine_service_pharmacy_mapping", "tableTo": "pharmacies", "schemaTo": "public", "columnsFrom": ["pharmacy_id"], "columnsTo": ["pharmacy_id"], "onDelete": "no action", "onUpdate": "no action"}, "medicine_service_pharmacy_mapping_service_id_fkey": {"name": "medicine_service_pharmacy_mapping_service_id_fkey", "tableFrom": "medicine_service_pharmacy_mapping", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chat_rooms": {"name": "chat_rooms", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "room_name": {"name": "room_name", "type": "text", "primaryKey": false, "notNull": true}, "room_identifier": {"name": "room_identifier", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "service_key": {"name": "service_key", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "last_message_at": {"name": "last_message_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_message": {"name": "last_message", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chat_room_members": {"name": "chat_room_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_room_members_user_id_users_user_id_fk": {"name": "chat_room_members_user_id_users_user_id_fk", "tableFrom": "chat_room_members", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_room_members_room_id_chat_rooms_id_fk": {"name": "chat_room_members_room_id_chat_rooms_id_fk", "tableFrom": "chat_room_members", "tableTo": "chat_rooms", "schemaTo": "public", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.transactions": {"name": "transactions", "schema": "", "columns": {"transaction_id": {"name": "transaction_id", "type": "text", "primaryKey": true, "notNull": true}, "payer_user_id": {"name": "payer_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "payee_user_id": {"name": "payee_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "double precision", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "enum_transactions_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "error_description": {"name": "error_description", "type": "text", "primaryKey": false, "notNull": false}, "card_details": {"name": "card_details", "type": "text", "primaryKey": false, "notNull": false}, "line_items": {"name": "line_items", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "global_id": {"name": "global_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "refund_transaction_id": {"name": "refund_transaction_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "refund_created_at": {"name": "refund_created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "refund_global_id": {"name": "refund_global_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "NULL"}, "payment_method_type": {"name": "payment_method_type", "type": "enum_transactions_payment_method_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'BRAINTREE'"}, "refund_payment_status": {"name": "refund_payment_status", "type": "enum_transactions_refund_payment_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'n/a'"}, "refund_payment_amount": {"name": "refund_payment_amount", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "refund_error_description": {"name": "refund_error_description", "type": "text", "primaryKey": false, "notNull": false}, "refund_success_response": {"name": "refund_success_response", "type": "text", "primaryKey": false, "notNull": false}, "transaction_status": {"name": "transaction_status", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"transactions_payee_user_id_fkey": {"name": "transactions_payee_user_id_fkey", "tableFrom": "transactions", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["payee_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "transactions_payer_user_id_fkey": {"name": "transactions_payer_user_id_fkey", "tableFrom": "transactions", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["payer_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.special_discounts": {"name": "special_discounts", "schema": "", "columns": {"discount_id": {"name": "discount_id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "discount_price": {"name": "discount_price", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.stripe_user_payment_details": {"name": "stripe_user_payment_details", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "payment_method_id": {"name": "payment_method_id", "type": "text", "primaryKey": false, "notNull": false}, "off_session_payment_allowed": {"name": "off_session_payment_allowed", "type": "boolean", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "enum_stripe_user_payment_details_payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'success'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"stripe_user_payment_details_user_id_fkey": {"name": "stripe_user_payment_details_user_id_fkey", "tableFrom": "stripe_user_payment_details", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_procedure_codes_mapping": {"name": "telehealth_service_procedure_codes_mapping", "schema": "", "columns": {"telehealth_service_procedure_codes_mapping_id": {"name": "telehealth_service_procedure_codes_mapping_id", "type": "serial", "primaryKey": true, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": true}, "procedure_code_id": {"name": "procedure_code_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_service_procedure_codes_mapping_service_id_fkey": {"name": "telehealth_service_procedure_codes_mapping_service_id_fkey", "tableFrom": "telehealth_service_procedure_codes_mapping", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_procedure_codes_mappi_procedure_code_id_fkey": {"name": "telehealth_service_procedure_codes_mappi_procedure_code_id_fkey", "tableFrom": "telehealth_service_procedure_codes_mapping", "tableTo": "procedure_codes", "schemaTo": "public", "columnsFrom": ["procedure_code_id"], "columnsTo": ["procedure_code_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_questions": {"name": "telehealth_service_questions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "question": {"name": "question", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "help_text": {"name": "help_text", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "selection_option": {"name": "selection_option", "type": "text", "primaryKey": false, "notNull": false}, "question_type": {"name": "question_type", "type": "enum_telehealth_service_questions_question_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'YesNo'"}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "halt_on_selection_option": {"name": "halt_on_selection_option", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "is_pre_questions": {"name": "is_pre_questions", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_optional": {"name": "is_optional", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "question_for": {"name": "question_for", "type": "enum_telehealth_service_questions_question_for", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'both'"}}, "indexes": {}, "foreignKeys": {"telehealth_service_questions_service_id_fkey": {"name": "telehealth_service_questions_service_id_fkey", "tableFrom": "telehealth_service_questions", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_provider_mapping": {"name": "telehealth_service_provider_mapping", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "provider_id": {"name": "provider_id", "type": "integer", "primaryKey": false, "notNull": false}, "cost_price": {"name": "cost_price", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {"telehealth_service_provider_mapping_provider_id_fkey": {"name": "telehealth_service_provider_mapping_provider_id_fkey", "tableFrom": "telehealth_service_provider_mapping", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["provider_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_provider_mapping_service_id_fkey": {"name": "telehealth_service_provider_mapping_service_id_fkey", "tableFrom": "telehealth_service_provider_mapping", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.subscription_plans": {"name": "subscription_plans", "schema": "", "columns": {"plan_id": {"name": "plan_id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "billing_cycle": {"name": "billing_cycle", "type": "enum_subscription_plans_billing_cycle", "typeSchema": "public", "primaryKey": false, "notNull": true}, "billing_interval": {"name": "billing_interval", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "paypal_plan_id": {"name": "paypal_plan_id", "type": "text", "primaryKey": false, "notNull": false}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "service_key": {"name": "service_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "service_master_id": {"name": "service_master_id", "type": "integer", "primaryKey": false, "notNull": false}, "recurly_plan_id": {"name": "recurly_plan_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_plan_id": {"name": "stripe_plan_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"subscription_plans_service_master_id_fkey": {"name": "subscription_plans_service_master_id_fkey", "tableFrom": "subscription_plans", "tableTo": "telehealth_service_master", "schemaTo": "public", "columnsFrom": ["service_master_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.stripe_user_details": {"name": "stripe_user_details", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": true, "notNull": true}, "stripe_user_id": {"name": "stripe_user_id", "type": "text", "primaryKey": false, "notNull": true}, "cc_status": {"name": "cc_status", "type": "enum_stripe_user_details_cc_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'not_captured'"}, "oauth_status": {"name": "oauth_status", "type": "enum_stripe_user_details_oauth_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'not_connected'"}, "stripe_account_id": {"name": "stripe_account_id", "type": "text", "primaryKey": false, "notNull": false}, "oauth_verification_token": {"name": "oauth_verification_token", "type": "text", "primaryKey": false, "notNull": false}, "currency_code": {"name": "currency_code", "type": "text", "primaryKey": false, "notNull": false}, "default_payment_method": {"name": "default_payment_method", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"stripe_user_details_user_id_fkey": {"name": "stripe_user_details_user_id_fkey", "tableFrom": "stripe_user_details", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.social_history_values": {"name": "social_history_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"social_history_values_summary_id_fkey": {"name": "social_history_values_summary_id_fkey", "tableFrom": "social_history_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_particlehealth": {"name": "user_particlehealth", "schema": "", "columns": {"health_id": {"name": "health_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "query_id": {"name": "query_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "buser_id": {"name": "buser_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_particlehealth_user_id_fkey": {"name": "user_particlehealth_user_id_fkey", "tableFrom": "user_particlehealth", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_particlehealth_buser_id_fkey": {"name": "user_particlehealth_buser_id_fkey", "tableFrom": "user_particlehealth", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["buser_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_details": {"name": "user_details", "schema": "", "columns": {"user_detail_id": {"name": "user_detail_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "signature": {"name": "signature", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "specialty": {"name": "specialty", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}}, "indexes": {}, "foreignKeys": {"user_details_user_id_fkey": {"name": "user_details_user_id_fkey", "tableFrom": "user_details", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_educational_videos": {"name": "user_educational_videos", "schema": "", "columns": {"user_video_id": {"name": "user_video_id", "type": "serial", "primaryKey": true, "notNull": true}, "video_id": {"name": "video_id", "type": "serial", "primaryKey": false, "notNull": true}, "referred_by": {"name": "referred_by", "type": "integer", "primaryKey": false, "notNull": true}, "referred_for": {"name": "referred_for", "type": "integer", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "viewed_at": {"name": "viewed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "viewed": {"name": "viewed", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_educational_videos_assigned_by_fkey": {"name": "user_educational_videos_assigned_by_fkey", "tableFrom": "user_educational_videos", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["referred_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_educational_videos_assigned_to_fkey": {"name": "user_educational_videos_assigned_to_fkey", "tableFrom": "user_educational_videos", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["referred_for"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_educational_videos_doctor_id_fkey": {"name": "user_educational_videos_doctor_id_fkey", "tableFrom": "user_educational_videos", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_educational_videos_video_id_fkey": {"name": "user_educational_videos_video_id_fkey", "tableFrom": "user_educational_videos", "tableTo": "educational_videos", "schemaTo": "public", "columnsFrom": ["video_id"], "columnsTo": ["video_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"product_id": {"name": "product_id", "type": "serial", "primaryKey": true, "notNull": true}, "product_name": {"name": "product_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_identities": {"name": "user_identities", "schema": "", "columns": {"user_identity_id": {"name": "user_identity_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "install_type": {"name": "install_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_identities_user_id_fkey": {"name": "user_identities_user_id_fkey", "tableFrom": "user_identities", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.follow_up_reminder": {"name": "follow_up_reminder", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "last_follow_up_sent": {"name": "last_follow_up_sent", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "next_follow_up": {"name": "next_follow_up", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_follow_up_reminder_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'pending'"}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}, "next_order_id": {"name": "next_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "follow_up_sent_date": {"name": "follow_up_sent_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"follow_up_reminder_order_id_fkey": {"name": "follow_up_reminder_order_id_fkey", "tableFrom": "follow_up_reminder", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "follow_up_reminder_user_id_fkey": {"name": "follow_up_reminder_user_id_fkey", "tableFrom": "follow_up_reminder", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "follow_up_reminder_tennant_id_fkey": {"name": "follow_up_reminder_tennant_id_fkey", "tableFrom": "follow_up_reminder", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tennant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "follow_up_reminder_next_order_id_fkey": {"name": "follow_up_reminder_next_order_id_fkey", "tableFrom": "follow_up_reminder", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["next_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_file_repo_details": {"name": "user_file_repo_details", "schema": "", "columns": {"repo_id": {"name": "repo_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "upload_type": {"name": "upload_type", "type": "enum_user_file_repo_details_upload_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "connection_details": {"name": "connection_details", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_file_repo_details_user_id_fkey": {"name": "user_file_repo_details_user_id_fkey", "tableFrom": "user_file_repo_details", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_health_summary": {"name": "user_health_summary", "schema": "", "columns": {"summary_id": {"name": "summary_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "source_platform": {"name": "source_platform", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "has_detail": {"name": "has_detail", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_health_summary_user_id_fkey": {"name": "user_health_summary_user_id_fkey", "tableFrom": "user_health_summary", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_subscription_billing": {"name": "user_subscription_billing", "schema": "", "columns": {"billing_id": {"name": "billing_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "integer", "primaryKey": false, "notNull": true}, "base_plan_price": {"name": "base_plan_price", "type": "integer", "primaryKey": false, "notNull": true}, "discount": {"name": "discount", "type": "integer", "primaryKey": false, "notNull": true}, "invoice_number": {"name": "invoice_number", "type": "text", "primaryKey": false, "notNull": false}, "special_discounts": {"name": "special_discounts", "type": "text", "primaryKey": false, "notNull": false}, "addons": {"name": "addons", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "total_price": {"name": "total_price", "type": "text", "primaryKey": false, "notNull": false, "default": "0"}, "billed": {"name": "billed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "invoice_pdf_path": {"name": "invoice_pdf_path", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_subscription_billing_plan_id_fkey": {"name": "user_subscription_billing_plan_id_fkey", "tableFrom": "user_subscription_billing", "tableTo": "subscription_plans", "schemaTo": "public", "columnsFrom": ["plan_id"], "columnsTo": ["plan_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_subscription_billing_user_id_fkey": {"name": "user_subscription_billing_user_id_fkey", "tableFrom": "user_subscription_billing", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.drug_days": {"name": "drug_days", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "drug_id": {"name": "drug_id", "type": "integer", "primaryKey": false, "notNull": false}, "drug_days": {"name": "drug_days", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"drug_days_drug_id_fkey": {"name": "drug_days_drug_id_fkey", "tableFrom": "drug_days", "tableTo": "drugs", "schemaTo": "public", "columnsFrom": ["drug_id"], "columnsTo": ["drug_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.consult_order_files": {"name": "consult_order_files", "schema": "", "columns": {"file_id": {"name": "file_id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "enum_consult_order_files_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"consult_order_files_order_id_fkey": {"name": "consult_order_files_order_id_fkey", "tableFrom": "consult_order_files", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_order_files_tennant_id_fkey": {"name": "consult_order_files_tennant_id_fkey", "tableFrom": "consult_order_files", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tennant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_subscription": {"name": "user_subscription", "schema": "", "columns": {"user_subscription_id": {"name": "user_subscription_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "no_of_intervals": {"name": "no_of_intervals", "type": "integer", "primaryKey": false, "notNull": false}, "referral_code": {"name": "referral_code", "type": "text", "primaryKey": false, "notNull": false}, "discount": {"name": "discount", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "next_billing_date": {"name": "next_billing_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "service_key": {"name": "service_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "service_master_id": {"name": "service_master_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_subscription_plan_id_fkey": {"name": "user_subscription_plan_id_fkey", "tableFrom": "user_subscription", "tableTo": "subscription_plans", "schemaTo": "public", "columnsFrom": ["plan_id"], "columnsTo": ["plan_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_subscription_user_id_fkey": {"name": "user_subscription_user_id_fkey", "tableFrom": "user_subscription", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_subscription_service_master_id_fkey": {"name": "user_subscription_service_master_id_fkey", "tableFrom": "user_subscription", "tableTo": "telehealth_service_master", "schemaTo": "public", "columnsFrom": ["service_master_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.sms_template": {"name": "sms_template", "schema": "", "columns": {"sms_template_id": {"name": "sms_template_id", "type": "serial", "primaryKey": true, "notNull": true}, "sms_action": {"name": "sms_action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "sms_template_name": {"name": "sms_template_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"sms_template_service_id_fkey": {"name": "sms_template_service_id_fkey", "tableFrom": "sms_template", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_vitals_documents": {"name": "user_vitals_documents", "schema": "", "columns": {"user_vitals_document_id": {"name": "user_vitals_document_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_vitals_documents_doctor_id_fkey": {"name": "user_vitals_documents_doctor_id_fkey", "tableFrom": "user_vitals_documents", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_vitals_documents_user_id_fkey": {"name": "user_vitals_documents_user_id_fkey", "tableFrom": "user_vitals_documents", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "sender_id": {"name": "sender_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "file_id": {"name": "file_id", "type": "integer", "primaryKey": false, "notNull": false}, "read_by": {"name": "read_by", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{\"RAY\"}'"}}, "indexes": {}, "foreignKeys": {"chat_messages_sender_id_users_user_id_fk": {"name": "chat_messages_sender_id_users_user_id_fk", "tableFrom": "chat_messages", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["sender_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_messages_room_id_chat_rooms_id_fk": {"name": "chat_messages_room_id_chat_rooms_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_rooms", "schemaTo": "public", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.jobs": {"name": "jobs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": true}, "referral_id": {"name": "referral_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "enum_jobs_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'pending'"}, "failed_message": {"name": "failed_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"jobs_order_id_fkey": {"name": "jobs_order_id_fkey", "tableFrom": "jobs", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "jobs_referral_id_fkey": {"name": "jobs_referral_id_fkey", "tableFrom": "jobs", "tableTo": "referrals", "schemaTo": "public", "columnsFrom": ["referral_id"], "columnsTo": ["referral_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_schedules": {"name": "user_schedules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "is_available": {"name": "is_available", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "end_datetime": {"name": "end_datetime", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "integer", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "integer", "primaryKey": false, "notNull": false}, "schedule_date": {"name": "schedule_date", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "default": "''"}, "start_datetime": {"name": "start_datetime", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_schedules_user_id_fkey": {"name": "user_schedules_user_id_fkey", "tableFrom": "user_schedules", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_schedules_created_by_fkey": {"name": "user_schedules_created_by_fkey", "tableFrom": "user_schedules", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_schedules_updated_by_fkey": {"name": "user_schedules_updated_by_fkey", "tableFrom": "user_schedules", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["updated_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_schedules_deleted_by_fkey": {"name": "user_schedules_deleted_by_fkey", "tableFrom": "user_schedules", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["deleted_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chat_files": {"name": "chat_files", "schema": "", "columns": {"file_id": {"name": "file_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "serial", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_files_user_id_users_user_id_fk": {"name": "chat_files_user_id_users_user_id_fk", "tableFrom": "chat_files", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_files_room_id_chat_rooms_id_fk": {"name": "chat_files_room_id_chat_rooms_id_fk", "tableFrom": "chat_files", "tableTo": "chat_rooms", "schemaTo": "public", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.email_template": {"name": "email_template", "schema": "", "columns": {"email_template_id": {"name": "email_template_id", "type": "serial", "primaryKey": true, "notNull": true}, "email_action": {"name": "email_action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email_template_name": {"name": "email_template_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"email_template_service_id_fkey": {"name": "email_template_service_id_fkey", "tableFrom": "email_template", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.prescription_preference": {"name": "prescription_preference", "schema": "", "columns": {"preference_id": {"name": "preference_id", "type": "serial", "primaryKey": true, "notNull": true}, "pharmacy_id": {"name": "pharmacy_id", "type": "integer", "primaryKey": false, "notNull": false}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}, "pharmacy_name": {"name": "pharmacy_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "preference": {"name": "preference", "type": "enum_prescription_preference_preference", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'fax'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"prescription_preference_pharmacy_id_fkey": {"name": "prescription_preference_pharmacy_id_fkey", "tableFrom": "prescription_preference", "tableTo": "pharmacies", "schemaTo": "public", "columnsFrom": ["pharmacy_id"], "columnsTo": ["pharmacy_id"], "onDelete": "no action", "onUpdate": "no action"}, "prescription_preference_tennant_id_fkey": {"name": "prescription_preference_tennant_id_fkey", "tableFrom": "prescription_preference", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tennant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.lifefile_configuration": {"name": "lifefile_configuration", "schema": "", "columns": {"lifefile_config_id": {"name": "lifefile_config_id", "type": "serial", "primaryKey": true, "notNull": true}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}, "pharmacy_id": {"name": "pharmacy_id", "type": "integer", "primaryKey": false, "notNull": false}, "pharmacy_name": {"name": "pharmacy_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "lifefile_url": {"name": "lifefile_url", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "api_username": {"name": "api_username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "api_password": {"name": "api_password", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "practice_id": {"name": "practice_id", "type": "integer", "primaryKey": false, "notNull": true}, "practice_name": {"name": "practice_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "vendor_id": {"name": "vendor_id", "type": "integer", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "integer", "primaryKey": false, "notNull": true}, "network_id": {"name": "network_id", "type": "integer", "primaryKey": false, "notNull": true}, "network_name": {"name": "network_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "shipping_services": {"name": "shipping_services", "type": "enum_lifefile_configuration_shipping_services", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'7780'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"lifefile_configuration_tennant_id_fkey": {"name": "lifefile_configuration_tennant_id_fkey", "tableFrom": "lifefile_configuration", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tennant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lifefile_configuration_pharmacy_id_fkey": {"name": "lifefile_configuration_pharmacy_id_fkey", "tableFrom": "lifefile_configuration", "tableTo": "pharmacies", "schemaTo": "public", "columnsFrom": ["pharmacy_id"], "columnsTo": ["pharmacy_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tennant_master": {"name": "tennant_master", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tennant_name": {"name": "tennant_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "pharmacy_fax": {"name": "pharmacy_fax", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "filter_display": {"name": "filter_display", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "lab_fax_number": {"name": "lab_fax_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "support_email": {"name": "support_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "paypal_client_id": {"name": "paypal_client_id", "type": "text", "primaryKey": false, "notNull": false}, "paypal_client_secret": {"name": "paypal_client_secret", "type": "text", "primaryKey": false, "notNull": false}, "access_lab_client_number": {"name": "access_lab_client_number", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "twilio_account_sid": {"name": "twilio_account_sid", "type": "text", "primaryKey": false, "notNull": false}, "twilio_phone_number": {"name": "twilio_phone_number", "type": "text", "primaryKey": false, "notNull": false}, "twilio_auth_token": {"name": "twilio_auth_token", "type": "text", "primaryKey": false, "notNull": false}, "stripe_client_id": {"name": "stripe_client_id", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "stripe_client_secret": {"name": "stripe_client_secret", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "preferred_payment_gateway": {"name": "preferred_payment_gateway", "type": "enum_tennant_master_preferred_payment_gateway", "typeSchema": "public", "primaryKey": false, "notNull": false}, "mailchip_from_email": {"name": "mailchip_from_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "mailchip_from_email_name": {"name": "mailchip_from_email_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tennant_display_name": {"name": "tennant_display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tenant_access": {"name": "tenant_access", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "dosespot_client_id": {"name": "dosespot_client_id", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "dosespot_client_secret": {"name": "dosespot_client_secret", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "configured_domain": {"name": "configured_domain", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "tennant_guid": {"name": "tennant_guid", "type": "uuid", "primaryKey": false, "notNull": true, "default": "uuid_generate_v4()"}, "default_provider_id": {"name": "default_provider_id", "type": "integer", "primaryKey": false, "notNull": false}, "show_powered_by": {"name": "show_powered_by", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "favicon_url": {"name": "favicon_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"tennant_master_parent_id_fkey": {"name": "tennant_master_parent_id_fkey", "tableFrom": "tennant_master", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tennant_master_default_provider_id_fkey": {"name": "tennant_master_default_provider_id_fkey", "tableFrom": "tennant_master", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["default_provider_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.consult_reassign_history": {"name": "consult_reassign_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_order_id": {"name": "service_order_id", "type": "integer", "primaryKey": false, "notNull": true}, "previous_provider": {"name": "previous_provider", "type": "integer", "primaryKey": false, "notNull": true}, "updated_provider": {"name": "updated_provider", "type": "integer", "primaryKey": false, "notNull": true}, "reassigned_by": {"name": "reassigned_by", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"consult_reassign_history_service_order_id_fkey": {"name": "consult_reassign_history_service_order_id_fkey", "tableFrom": "consult_reassign_history", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_reassign_history_previous_provider_fkey": {"name": "consult_reassign_history_previous_provider_fkey", "tableFrom": "consult_reassign_history", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["previous_provider"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_reassign_history_updated_provider_fkey": {"name": "consult_reassign_history_updated_provider_fkey", "tableFrom": "consult_reassign_history", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["updated_provider"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "consult_reassign_history_reassigned_by_fkey": {"name": "consult_reassign_history_reassigned_by_fkey", "tableFrom": "consult_reassign_history", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["reassigned_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_question_answer_dump": {"name": "telehealth_service_question_answer_dump", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_order_id": {"name": "service_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "question_text": {"name": "question_text", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": true}, "answer": {"name": "answer", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false, "default": "false"}, "other_text": {"name": "other_text", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "selection_option": {"name": "selection_option", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_service_question_answer_dump_service_order_id_fkey": {"name": "telehealth_service_question_answer_dump_service_order_id_fkey", "tableFrom": "telehealth_service_question_answer_dump", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.external_requests_log": {"name": "external_requests_log", "schema": "", "columns": {"external_request_log_id": {"name": "external_request_log_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"external_requests_log_user_id_fkey": {"name": "external_requests_log_user_id_fkey", "tableFrom": "external_requests_log", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.mms_patient_invitations": {"name": "mms_patient_invitations", "schema": "", "columns": {"invitation_id": {"name": "invitation_id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "mms_patient_id": {"name": "mms_patient_id", "type": "integer", "primaryKey": false, "notNull": true}, "accepted": {"name": "accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "expiry": {"name": "expiry", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "remaining_tries": {"name": "remaining_tries", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"mms_patient_invitations_mms_patient_id_fkey": {"name": "mms_patient_invitations_mms_patient_id_fkey", "tableFrom": "mms_patient_invitations", "tableTo": "mms_patients", "schemaTo": "public", "columnsFrom": ["mms_patient_id"], "columnsTo": ["patient_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.pharmacies": {"name": "pharmacies", "schema": "", "columns": {"pharmacy_id": {"name": "pharmacy_id", "type": "serial", "primaryKey": true, "notNull": true}, "npi": {"name": "npi", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "nabp": {"name": "nabp", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "fax": {"name": "fax", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "ncpdpid": {"name": "ncpdpid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "pharmcist_name": {"name": "pharmcist_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "dea": {"name": "dea", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "pharmacy_legal_name": {"name": "pharmacy_legal_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "pharmacies_guid": {"name": "pharmacies_guid", "type": "uuid", "primaryKey": false, "notNull": true, "default": "uuid_generate_v4()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pharmacies_npi_key": {"columns": ["npi"], "nullsNotDistinct": false, "name": "pharmacies_npi_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.service_payment_mapping": {"name": "service_payment_mapping", "schema": "", "columns": {"service_payment_mapping_id": {"name": "service_payment_mapping_id", "type": "serial", "primaryKey": true, "notNull": true}, "service_key": {"name": "service_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "service_master_id": {"name": "service_master_id", "type": "integer", "primaryKey": false, "notNull": false}, "payment_details_id": {"name": "payment_details_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "payment_type": {"name": "payment_type", "type": "enum_service_payment_mapping_payment_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'subscription'"}}, "indexes": {}, "foreignKeys": {"service_payment_mapping_service_master_id_fkey": {"name": "service_payment_mapping_service_master_id_fkey", "tableFrom": "service_payment_mapping", "tableTo": "telehealth_service_master", "schemaTo": "public", "columnsFrom": ["service_master_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "service_payment_mapping_payment_details_id_fkey": {"name": "service_payment_mapping_payment_details_id_fkey", "tableFrom": "service_payment_mapping", "tableTo": "payment_details", "schemaTo": "public", "columnsFrom": ["payment_details_id"], "columnsTo": ["payment_details_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_guid": {"name": "user_guid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "enum_users_role", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'USER'"}, "status": {"name": "status", "type": "enum_users_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'OFFLINE'"}, "install_type": {"name": "install_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "otp": {"name": "otp", "type": "text", "primaryKey": false, "notNull": false}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": false}, "appt_length": {"name": "appt_length", "type": "integer", "primaryKey": false, "notNull": false}, "appt_start_time": {"name": "appt_start_time", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "appt_end_time": {"name": "appt_end_time", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "secure_message": {"name": "secure_message", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "connection_requests": {"name": "connection_requests", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "vitals_ccd_enabled": {"name": "vitals_ccd_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "appt_requests": {"name": "appt_requests", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "trial_validity": {"name": "trial_validity", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_cc_captured": {"name": "is_cc_captured", "type": "boolean", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "enum_users_gender", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'male'"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "user_avatar": {"name": "user_avatar", "type": "text", "primaryKey": false, "notNull": false}, "id_card": {"name": "id_card", "type": "text", "primaryKey": false, "notNull": false}, "history": {"name": "history", "type": "text", "primaryKey": false, "notNull": false}, "questionnaire": {"name": "questionnaire", "type": "text", "primaryKey": false, "notNull": false}, "token_validity": {"name": "token_validity", "type": "integer", "primaryKey": false, "notNull": false}, "email_verification_details": {"name": "email_verification_details", "type": "text", "primaryKey": false, "notNull": false}, "last_active": {"name": "last_active", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "locked": {"name": "locked", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "registration_key": {"name": "registration_key", "type": "text", "primaryKey": false, "notNull": false}, "is_rpm_enabled": {"name": "is_rpm_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_notify_on_capture": {"name": "is_notify_on_capture", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "vitals_thresholds": {"name": "vitals_thresholds", "type": "text", "primaryKey": false, "notNull": false}, "vitals_cron": {"name": "vitals_cron", "type": "text", "primaryKey": false, "notNull": false}, "app_details": {"name": "app_details", "type": "text", "primaryKey": false, "notNull": false}, "history_updated_at": {"name": "history_updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "questionnaire_updated_at": {"name": "questionnaire_updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "debug": {"name": "debug", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "invitation_code_validity": {"name": "invitation_code_validity", "type": "integer", "primaryKey": false, "notNull": false}, "cron_expression": {"name": "cron_expression", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "mirth_ccd_enabled": {"name": "mirth_ccd_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cc_payment_accepted": {"name": "cc_payment_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "recording_enabled": {"name": "recording_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "transcription_enabled": {"name": "transcription_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "orders_enabled": {"name": "orders_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "show_health_summaries": {"name": "show_health_summaries", "type": "boolean", "primaryKey": false, "notNull": false}, "healthgorilla_id": {"name": "healthgorilla_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "release_medical": {"name": "release_medical", "type": "boolean", "primaryKey": false, "notNull": false}, "telehealth_service_cost": {"name": "telehealth_service_cost", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "sub_role": {"name": "sub_role", "type": "enum_users_sub_role", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'USER'"}, "my_invite_code": {"name": "my_invite_code", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false, "default": "NULL"}, "referred_by_invite_code": {"name": "referred_by_invite_code", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false, "default": "NULL"}, "referred_by_user_id": {"name": "referred_by_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "email_2": {"name": "email_2", "type": "text", "primaryKey": false, "notNull": false}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}, "secondary_phone": {"name": "secondary_phone", "type": "text", "primaryKey": false, "notNull": false}, "is_tennant_owner": {"name": "is_tennant_owner", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "send_email_campaign": {"name": "send_email_campaign", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "app_timezone": {"name": "app_timezone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "'{}'"}, "rxPersonId": {"name": "rxPersonId", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "rxStatus": {"name": "rxStatus", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "tenant_access": {"name": "tenant_access", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "dosespot_api_response": {"name": "dosespot_api_response", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "id_card_file": {"name": "id_card_file", "type": "text", "primaryKey": false, "notNull": false}, "pharmacy_access": {"name": "pharmacy_access", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "dependent_account_relation": {"name": "dependent_account_relation", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tracking_code": {"name": "tracking_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_tennant_id_fkey": {"name": "users_tennant_id_fkey", "tableFrom": "users", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tennant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_parent_id_fkey": {"name": "users_parent_id_fkey", "tableFrom": "users", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["parent_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_my_invite_code_key": {"columns": ["my_invite_code"], "nullsNotDistinct": false, "name": "users_my_invite_code_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.referral_tracking": {"name": "referral_tracking", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "referral_id": {"name": "referral_id", "type": "integer", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "shipping_partner": {"name": "shipping_partner", "type": "enum_referral_tracking_shipping_partner", "typeSchema": "public", "primaryKey": false, "notNull": false}, "tracking_number": {"name": "tracking_number", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"referral_tracking_referral_id_fkey": {"name": "referral_tracking_referral_id_fkey", "tableFrom": "referral_tracking", "tableTo": "referrals", "schemaTo": "public", "columnsFrom": ["referral_id"], "columnsTo": ["referral_id"], "onDelete": "no action", "onUpdate": "no action"}, "referral_tracking_order_guid_fkey": {"name": "referral_tracking_order_guid_fkey", "tableFrom": "referral_tracking", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_guid"], "columnsTo": ["order_guid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.support_notes": {"name": "support_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "support_user_id": {"name": "support_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "general_note": {"name": "general_note", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"support_notes_user_id_fkey": {"name": "support_notes_user_id_fkey", "tableFrom": "support_notes", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "support_notes_support_user_id_fkey": {"name": "support_notes_support_user_id_fkey", "tableFrom": "support_notes", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["support_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "support_notes_order_guid_fkey": {"name": "support_notes_order_guid_fkey", "tableFrom": "support_notes", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_guid"], "columnsTo": ["order_guid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tenant_auth_provider": {"name": "tenant_auth_provider", "schema": "", "columns": {"auth_id": {"name": "auth_id", "type": "serial", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "secret_token": {"name": "secret_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "auth_token": {"name": "auth_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"tenant_auth_provider_tenant_id_fkey": {"name": "tenant_auth_provider_tenant_id_fkey", "tableFrom": "tenant_auth_provider", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tennant_config": {"name": "tennant_config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}, "theme_color": {"name": "theme_color", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"tennant_config_tennant_id_fkey": {"name": "tennant_config_tennant_id_fkey", "tableFrom": "tennant_config", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tennant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.service_action_preference": {"name": "service_action_preference", "schema": "", "columns": {"service_action_preference_id": {"name": "service_action_preference_id", "type": "serial", "primaryKey": true, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "action_type": {"name": "action_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "webhook_url": {"name": "webhook_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sms_template": {"name": "sms_template", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email_template": {"name": "email_template", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "external_api_integration": {"name": "external_api_integration", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "api_key": {"name": "api_key", "type": "text", "primaryKey": false, "notNull": false}, "api_secret": {"name": "api_secret", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"service_action_preference_service_id_fkey": {"name": "service_action_preference_service_id_fkey", "tableFrom": "service_action_preference", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "service_action_preference_tenant_id_fkey": {"name": "service_action_preference_tenant_id_fkey", "tableFrom": "service_action_preference", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_master": {"name": "telehealth_service_master", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "service_master_guid": {"name": "service_master_guid", "type": "uuid", "primaryKey": false, "notNull": true, "default": "uuid_generate_v4()"}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "service_key": {"name": "service_key", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "initial_service_id": {"name": "initial_service_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_service_master_tennant_id_fkey": {"name": "telehealth_service_master_tennant_id_fkey", "tableFrom": "telehealth_service_master", "tableTo": "tennant_master", "schemaTo": "public", "columnsFrom": ["tennant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_master_initial_service_id_fkey": {"name": "telehealth_service_master_initial_service_id_fkey", "tableFrom": "telehealth_service_master", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["initial_service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_services": {"name": "telehealth_services", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_name": {"name": "service_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "service_type": {"name": "service_type", "type": "enum_telehealth_services_service_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'BUSER'"}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "service_mode": {"name": "service_mode", "type": "enum_telehealth_services_service_mode", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'BOTH_SYNC_ASYNC'"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "paypal_plan_id": {"name": "paypal_plan_id", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "subtitle": {"name": "subtitle", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "service_key": {"name": "service_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "session_type": {"name": "session_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "fields_options": {"name": "fields_options", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "on_complete_script": {"name": "on_complete_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_follow_up_script": {"name": "on_follow_up_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "access_labs_test_code": {"name": "access_labs_test_code", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_create_script": {"name": "on_create_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_lab_result_received_script": {"name": "on_lab_result_received_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "on_update_schedule_script": {"name": "on_update_schedule_script", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "disclaimer": {"name": "disclaimer", "type": "text", "primaryKey": false, "notNull": false}, "service_details": {"name": "service_details", "type": "text", "primaryKey": false, "notNull": false}, "is_video_call": {"name": "is_video_call", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_audio_call": {"name": "is_audio_call", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "display_questionnaire": {"name": "display_questionnaire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "display_service_name": {"name": "display_service_name", "type": "text", "primaryKey": false, "notNull": false}, "erx_drug_key": {"name": "erx_drug_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "filter_display": {"name": "filter_display", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "services_guid": {"name": "services_guid", "type": "uuid", "primaryKey": false, "notNull": true, "default": "uuid_generate_v4()"}, "original_amount": {"name": "original_amount", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "duration_text": {"name": "duration_text", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "discount_text": {"name": "discount_text", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "consult_instruction": {"name": "consult_instruction", "type": "json", "primaryKey": false, "notNull": false}, "service_instruction": {"name": "service_instruction", "type": "json", "primaryKey": false, "notNull": false}, "service_master_id": {"name": "service_master_id", "type": "integer", "primaryKey": false, "notNull": false}, "next_service_id": {"name": "next_service_id", "type": "integer", "primaryKey": false, "notNull": false}, "eligible_messages": {"name": "eligible_messages", "type": "json", "primaryKey": false, "notNull": false}, "display_amount": {"name": "display_amount", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "NULL"}, "user_consent": {"name": "user_consent", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_services_service_master_id_fkey": {"name": "telehealth_services_service_master_id_fkey", "tableFrom": "telehealth_services", "tableTo": "telehealth_service_master", "schemaTo": "public", "columnsFrom": ["service_master_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_services_next_service_id_fkey": {"name": "telehealth_services_next_service_id_fkey", "tableFrom": "telehealth_services", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["next_service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"telehealth_services_service_name_key": {"columns": ["service_name"], "nullsNotDistinct": false, "name": "telehealth_services_service_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.diagnoses_values": {"name": "diagnoses_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"diagnoses_values_summary_id_fkey": {"name": "diagnoses_values_summary_id_fkey", "tableFrom": "diagnoses_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.family_history_values": {"name": "family_history_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"family_history_values_summary_id_fkey": {"name": "family_history_values_summary_id_fkey", "tableFrom": "family_history_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.procedures_values": {"name": "procedures_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"procedures_values_summary_id_fkey": {"name": "procedures_values_summary_id_fkey", "tableFrom": "procedures_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.care_plan_values": {"name": "care_plan_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"care_plan_values_summary_id_fkey": {"name": "care_plan_values_summary_id_fkey", "tableFrom": "care_plan_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.results_values": {"name": "results_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"results_values_summary_id_fkey": {"name": "results_values_summary_id_fkey", "tableFrom": "results_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.allergies_values": {"name": "allergies_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"allergies_values_summary_id_fkey": {"name": "allergies_values_summary_id_fkey", "tableFrom": "allergies_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.document_values": {"name": "document_values", "schema": "", "columns": {"value_id": {"name": "value_id", "type": "serial", "primaryKey": true, "notNull": true}, "summary_id": {"name": "summary_id", "type": "integer", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"document_values_summary_id_fkey": {"name": "document_values_summary_id_fkey", "tableFrom": "document_values", "tableTo": "user_health_summary", "schemaTo": "public", "columnsFrom": ["summary_id"], "columnsTo": ["summary_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.mms_prescription_refills": {"name": "mms_prescription_refills", "schema": "", "columns": {"refill_id": {"name": "refill_id", "type": "serial", "primaryKey": true, "notNull": true}, "prescription_id": {"name": "prescription_id", "type": "integer", "primaryKey": false, "notNull": true}, "refill_no": {"name": "refill_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_filled": {"name": "date_filled", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_picked": {"name": "date_picked", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "qty_ordered": {"name": "qty_ordered", "type": "numeric", "primaryKey": false, "notNull": false}, "qty_dispensed": {"name": "qty_dispensed", "type": "numeric", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "delivery_method": {"name": "delivery_method", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cost_price": {"name": "cost_price", "type": "numeric", "primaryKey": false, "notNull": false}, "disp_fee": {"name": "disp_fee", "type": "numeric", "primaryKey": false, "notNull": false}, "rx_amount": {"name": "rx_amount", "type": "numeric", "primaryKey": false, "notNull": false}, "patient_copay": {"name": "patient_copay", "type": "numeric", "primaryKey": false, "notNull": false}, "billed_amount": {"name": "billed_amount", "type": "numeric", "primaryKey": false, "notNull": false}, "primary_insurance_amount": {"name": "primary_insurance_amount", "type": "numeric", "primaryKey": false, "notNull": false}, "primary_insurance_code": {"name": "primary_insurance_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "secondary_insurance_amount": {"name": "secondary_insurance_amount", "type": "numeric", "primaryKey": false, "notNull": false}, "tertiary_insurance_amount": {"name": "tertiary_insurance_amount", "type": "numeric", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"mms_prescription_refills_prescription_id_fkey": {"name": "mms_prescription_refills_prescription_id_fkey", "tableFrom": "mms_prescription_refills", "tableTo": "mms_prescriptions", "schemaTo": "public", "columnsFrom": ["prescription_id"], "columnsTo": ["prescription_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vitals_summary_upload_status": {"name": "vitals_summary_upload_status", "schema": "", "columns": {"status_id": {"name": "status_id", "type": "serial", "primaryKey": true, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": true}, "patient_id": {"name": "patient_id", "type": "integer", "primaryKey": false, "notNull": true}, "upload_type": {"name": "upload_type", "type": "enum_vitals_summary_upload_status_upload_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "upload_status": {"name": "upload_status", "type": "enum_vitals_summary_upload_status_upload_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'SUCCESS'"}, "doc_path": {"name": "doc_path", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "fax_id": {"name": "fax_id", "type": "integer", "primaryKey": false, "notNull": false}, "fax_status": {"name": "fax_status", "type": "text", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"vitals_summary_upload_status_doctor_id_fkey": {"name": "vitals_summary_upload_status_doctor_id_fkey", "tableFrom": "vitals_summary_upload_status", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "vitals_summary_upload_status_patient_id_fkey": {"name": "vitals_summary_upload_status_patient_id_fkey", "tableFrom": "vitals_summary_upload_status", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vitals_notification_cycle": {"name": "vitals_notification_cycle", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": true, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"vitals_notification_cycle_user_id_fkey": {"name": "vitals_notification_cycle_user_id_fkey", "tableFrom": "vitals_notification_cycle", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.visit_summary_upload_status": {"name": "visit_summary_upload_status", "schema": "", "columns": {"status_id": {"name": "status_id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "upload_type": {"name": "upload_type", "type": "enum_visit_summary_upload_status_upload_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "upload_status": {"name": "upload_status", "type": "enum_visit_summary_upload_status_upload_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'SUCCESS'"}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"visit_summary_upload_status_order_id_fkey": {"name": "visit_summary_upload_status_order_id_fkey", "tableFrom": "visit_summary_upload_status", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["order_id"], "onDelete": "no action", "onUpdate": "no action"}, "visit_summary_upload_status_user_id_fkey": {"name": "visit_summary_upload_status_user_id_fkey", "tableFrom": "visit_summary_upload_status", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vitals_patient_monitoring": {"name": "vitals_patient_monitoring", "schema": "", "columns": {"patient_id": {"name": "patient_id", "type": "integer", "primaryKey": false, "notNull": true}, "buser_id": {"name": "buser_id", "type": "integer", "primaryKey": false, "notNull": true}, "group_id": {"name": "group_id", "type": "serial", "primaryKey": true, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "billed": {"name": "billed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "billed_date": {"name": "billed_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "procedure": {"name": "procedure", "type": "text", "primaryKey": false, "notNull": false, "default": "'{\"CPT_codes\":[{\"code\":\"99454\",\"description\":\"Device(s) supply with daily recording(s) or programmed alert(s) transmission, each 30 days\"}]}'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"vitals_patient_monitoring_buser_id_fkey": {"name": "vitals_patient_monitoring_buser_id_fkey", "tableFrom": "vitals_patient_monitoring", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["buser_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "vitals_patient_monitoring_patient_id_fkey": {"name": "vitals_patient_monitoring_patient_id_fkey", "tableFrom": "vitals_patient_monitoring", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_forms": {"name": "user_forms", "schema": "", "columns": {"user_form_id": {"name": "user_form_id", "type": "serial", "primaryKey": true, "notNull": true}, "form_id": {"name": "form_id", "type": "integer", "primaryKey": false, "notNull": false}, "assigned_by": {"name": "assigned_by", "type": "integer", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "integer", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": false}, "filled_form_url": {"name": "filled_form_url", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "score": {"name": "score", "type": "numeric", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_forms_assigned_by_fkey": {"name": "user_forms_assigned_by_fkey", "tableFrom": "user_forms", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["assigned_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_forms_assigned_to_fkey": {"name": "user_forms_assigned_to_fkey", "tableFrom": "user_forms", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["assigned_to"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_forms_doctor_id_fkey": {"name": "user_forms_doctor_id_fkey", "tableFrom": "user_forms", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_forms_form_id_fkey": {"name": "user_forms_form_id_fkey", "tableFrom": "user_forms", "tableTo": "forms", "schemaTo": "public", "columnsFrom": ["form_id"], "columnsTo": ["form_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_forms_order_id_fkey": {"name": "user_forms_order_id_fkey", "tableFrom": "user_forms", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_files": {"name": "user_files", "schema": "", "columns": {"user_file_id": {"name": "user_file_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_files_created_by_fkey": {"name": "user_files_created_by_fkey", "tableFrom": "user_files", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_doctor_id_fkey": {"name": "user_files_doctor_id_fkey", "tableFrom": "user_files", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_order_id_fkey": {"name": "user_files_order_id_fkey", "tableFrom": "user_files", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_user_id_fkey": {"name": "user_files_user_id_fkey", "tableFrom": "user_files", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_diet": {"name": "user_diet", "schema": "", "columns": {"user_diet_id": {"name": "user_diet_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "meal_type": {"name": "meal_type", "type": "enum_user_diet_meal_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "serving": {"name": "serving", "type": "integer", "primaryKey": false, "notNull": false}, "serving_unit": {"name": "serving_unit", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "food": {"name": "food", "type": "text", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_diet_order_id_fkey": {"name": "user_diet_order_id_fkey", "tableFrom": "user_diet", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_diet_user_id_fkey": {"name": "user_diet_user_id_fkey", "tableFrom": "user_diet", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_question_answer": {"name": "telehealth_service_question_answer", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_order_id": {"name": "service_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "question_id": {"name": "question_id", "type": "integer", "primaryKey": false, "notNull": false}, "answer": {"name": "answer", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "other_text": {"name": "other_text", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_service_question_answer_question_id_fkey": {"name": "telehealth_service_question_answer_question_id_fkey", "tableFrom": "telehealth_service_question_answer", "tableTo": "telehealth_service_questions", "schemaTo": "public", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_question_answer_service_order_id_fkey": {"name": "telehealth_service_question_answer_service_order_id_fkey", "tableFrom": "telehealth_service_question_answer", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.faxes": {"name": "faxes", "schema": "", "columns": {"fax_id": {"name": "fax_id", "type": "serial", "primaryKey": true, "notNull": true}, "fax_sid": {"name": "fax_sid", "type": "text", "primaryKey": false, "notNull": true}, "fax_number": {"name": "fax_number", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "referral_id": {"name": "referral_id", "type": "integer", "primaryKey": false, "notNull": false}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": false}, "media_url": {"name": "media_url", "type": "text", "primaryKey": false, "notNull": false}, "sent_by": {"name": "sent_by", "type": "integer", "primaryKey": false, "notNull": false}, "sent_for": {"name": "sent_for", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"faxes_referral_id_fkey": {"name": "faxes_referral_id_fkey", "tableFrom": "faxes", "tableTo": "referrals", "schemaTo": "public", "columnsFrom": ["referral_id"], "columnsTo": ["referral_id"], "onDelete": "no action", "onUpdate": "no action"}, "faxes_sent_by_fkey": {"name": "faxes_sent_by_fkey", "tableFrom": "faxes", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["sent_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "faxes_sent_for_fkey": {"name": "faxes_sent_for_fkey", "tableFrom": "faxes", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["sent_for"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"review_id": {"name": "review_id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false}, "review": {"name": "review", "type": "text", "primaryKey": false, "notNull": true}, "given_by": {"name": "given_by", "type": "integer", "primaryKey": false, "notNull": false}, "given_to": {"name": "given_to", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "call_quality": {"name": "call_quality", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_recommend_provider": {"name": "is_recommend_provider", "type": "boolean", "primaryKey": false, "notNull": false}, "consultation_rating": {"name": "consultation_rating", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "user_comment": {"name": "user_comment", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}}, "indexes": {}, "foreignKeys": {"reviews_given_by_fkey": {"name": "reviews_given_by_fkey", "tableFrom": "reviews", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["given_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_given_to_fkey": {"name": "reviews_given_to_fkey", "tableFrom": "reviews", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["given_to"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_order_id_fkey": {"name": "reviews_order_id_fkey", "tableFrom": "reviews", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["order_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.referrals": {"name": "referrals", "schema": "", "columns": {"referral_id": {"name": "referral_id", "type": "serial", "primaryKey": true, "notNull": true}, "referred_by": {"name": "referred_by", "type": "integer", "primaryKey": false, "notNull": false}, "referred_for": {"name": "referred_for", "type": "integer", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "integer", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "enum_referrals_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": false}, "prescription_viewed": {"name": "prescription_viewed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "prescription_processed_by": {"name": "prescription_processed_by", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "lifefile_order_id": {"name": "lifefile_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "dosespot_prescription_id": {"name": "dosespot_prescription_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"referrals_doctor_id_fkey": {"name": "referrals_doctor_id_fkey", "tableFrom": "referrals", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "referrals_order_id_fkey": {"name": "referrals_order_id_fkey", "tableFrom": "referrals", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "referrals_referred_by_fkey": {"name": "referrals_referred_by_fkey", "tableFrom": "referrals", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["referred_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "referrals_referred_for_fkey": {"name": "referrals_referred_for_fkey", "tableFrom": "referrals", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["referred_for"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "referrals_product_id_fkey": {"name": "referrals_product_id_fkey", "tableFrom": "referrals", "tableTo": "products", "schemaTo": "public", "columnsFrom": ["product_id"], "columnsTo": ["product_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.transcriptions": {"name": "transcriptions", "schema": "", "columns": {"transcription_id": {"name": "transcription_id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false}, "tuser_id": {"name": "tuser_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "transcript": {"name": "transcript", "type": "text", "primaryKey": false, "notNull": false}, "updated_transcript": {"name": "updated_transcript", "type": "text", "primaryKey": false, "notNull": false}, "audio_stream": {"name": "audio_stream", "type": "text", "primaryKey": false, "notNull": false}, "archive_id": {"name": "archive_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"transcriptions_order_id_fkey": {"name": "transcriptions_order_id_fkey", "tableFrom": "transcriptions", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["order_id"], "onDelete": "no action", "onUpdate": "no action"}, "transcriptions_tuser_id_fkey": {"name": "transcriptions_tuser_id_fkey", "tableFrom": "transcriptions", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["tuser_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_vitals": {"name": "user_vitals", "schema": "", "columns": {"user_vital_id": {"name": "user_vital_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "metric": {"name": "metric", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "mode": {"name": "mode", "type": "enum_user_vitals_mode", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'automated'"}, "billed": {"name": "billed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "procedure": {"name": "procedure", "type": "text", "primaryKey": false, "notNull": false}, "abnormal": {"name": "abnormal", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "entity_id": {"name": "entity_id", "type": "integer", "primaryKey": false, "notNull": false}, "bundle_id": {"name": "bundle_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric", "primaryKey": false, "notNull": false}, "source_platform": {"name": "source_platform", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_vitals_user_id_date": {"name": "idx_user_vitals_user_id_date", "columns": [{"expression": "user_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}, {"expression": "date", "asc": false, "nulls": "first", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_vitals_order_id_fkey": {"name": "user_vitals_order_id_fkey", "tableFrom": "user_vitals", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_vitals_user_id_fkey": {"name": "user_vitals_user_id_fkey", "tableFrom": "user_vitals", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.provider_ratings": {"name": "provider_ratings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}, "provider_id": {"name": "provider_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"provider_ratings_order_id_fkey": {"name": "provider_ratings_order_id_fkey", "tableFrom": "provider_ratings", "tableTo": "orders", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "provider_ratings_provider_id_fkey": {"name": "provider_ratings_provider_id_fkey", "tableFrom": "provider_ratings", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["provider_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "provider_ratings_user_id_fkey": {"name": "provider_ratings_user_id_fkey", "tableFrom": "provider_ratings", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.schedules": {"name": "schedules", "schema": "", "columns": {"schedule_id": {"name": "schedule_id", "type": "serial", "primaryKey": true, "notNull": true}, "scheduled_with": {"name": "scheduled_with", "type": "integer", "primaryKey": false, "notNull": false}, "scheduled_by": {"name": "scheduled_by", "type": "integer", "primaryKey": false, "notNull": false}, "start_year": {"name": "start_year", "type": "integer", "primaryKey": false, "notNull": true}, "start_month": {"name": "start_month", "type": "integer", "primaryKey": false, "notNull": true}, "start_day": {"name": "start_day", "type": "integer", "primaryKey": false, "notNull": true}, "end_year": {"name": "end_year", "type": "integer", "primaryKey": false, "notNull": true}, "end_month": {"name": "end_month", "type": "integer", "primaryKey": false, "notNull": true}, "end_day": {"name": "end_day", "type": "integer", "primaryKey": false, "notNull": true}, "start": {"name": "start", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "end": {"name": "end", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": false}, "patient_history": {"name": "patient_history", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "order_guid": {"name": "order_guid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "NULL"}, "release_medical": {"name": "release_medical", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"schedules_order_guid_fkey": {"name": "schedules_order_guid_fkey", "tableFrom": "schedules", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_guid"], "columnsTo": ["order_guid"], "onDelete": "no action", "onUpdate": "no action"}, "schedules_scheduled_by_fkey": {"name": "schedules_scheduled_by_fkey", "tableFrom": "schedules", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["scheduled_by"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "schedules_scheduled_with_fkey": {"name": "schedules_scheduled_with_fkey", "tableFrom": "schedules", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["scheduled_with"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "schedules_order_id_fkey": {"name": "schedules_order_id_fkey", "tableFrom": "schedules", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tokbox_archive_type": {"name": "tokbox_archive_type", "schema": "", "columns": {"session_id": {"name": "session_id", "type": "text", "primaryKey": true, "notNull": true}, "archive_id": {"name": "archive_id", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.health_summary_metadata": {"name": "health_summary_metadata", "schema": "", "columns": {"metadata_id": {"name": "metadata_id", "type": "serial", "primaryKey": true, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "response_type": {"name": "response_type", "type": "text", "primaryKey": false, "notNull": false}, "template": {"name": "template", "type": "text", "primaryKey": false, "notNull": false}, "array_fields": {"name": "array_fields", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.health_summaries_log": {"name": "health_summaries_log", "schema": "", "columns": {"log_id": {"name": "log_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "summary_details": {"name": "summary_details", "type": "text", "primaryKey": false, "notNull": false}, "step": {"name": "step", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"health_summaries_log_user_id_fkey": {"name": "health_summaries_log_user_id_fkey", "tableFrom": "health_summaries_log", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.conversation_messages": {"name": "conversation_messages", "schema": "", "columns": {"cm_id": {"name": "cm_id", "type": "serial", "primaryKey": true, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "c_id": {"name": "c_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"conversation_messages_c_id_fkey": {"name": "conversation_messages_c_id_fkey", "tableFrom": "conversation_messages", "tableTo": "conversations", "schemaTo": "public", "columnsFrom": ["c_id"], "columnsTo": ["c_id"], "onDelete": "no action", "onUpdate": "no action"}, "conversation_messages_user_id_fkey": {"name": "conversation_messages_user_id_fkey", "tableFrom": "conversation_messages", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"c_id": {"name": "c_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_one": {"name": "user_one", "type": "integer", "primaryKey": false, "notNull": false}, "user_two": {"name": "user_two", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"conversations_user_one_fkey": {"name": "conversations_user_one_fkey", "tableFrom": "conversations", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_one"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "conversations_user_two_fkey": {"name": "conversations_user_two_fkey", "tableFrom": "conversations", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_two"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.prescription_transfer_medications": {"name": "prescription_transfer_medications", "schema": "", "columns": {"medication_id": {"name": "medication_id", "type": "serial", "primaryKey": true, "notNull": true}, "request_id": {"name": "request_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "is_fulfilled": {"name": "is_fulfilled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "hg_rx_id": {"name": "hg_rx_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_prescription_transfer_medications_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"prescription_transfer_medications_request_id_fkey": {"name": "prescription_transfer_medications_request_id_fkey", "tableFrom": "prescription_transfer_medications", "tableTo": "prescription_transfer_request", "schemaTo": "public", "columnsFrom": ["request_id"], "columnsTo": ["request_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.prescription_transfer_request": {"name": "prescription_transfer_request", "schema": "", "columns": {"request_id": {"name": "request_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "zipcode": {"name": "zipcode", "type": "text", "primaryKey": false, "notNull": true}, "pharmacy_name": {"name": "pharmacy_name", "type": "text", "primaryKey": false, "notNull": true}, "pharmacy_address": {"name": "pharmacy_address", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "transfer_all": {"name": "transfer_all", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"prescription_transfer_request_user_id_fkey": {"name": "prescription_transfer_request_user_id_fkey", "tableFrom": "prescription_transfer_request", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.refill_request": {"name": "refill_request", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_order_id": {"name": "service_order_id", "type": "integer", "primaryKey": false, "notNull": true}, "drug_details": {"name": "drug_details", "type": "<PERSON><PERSON><PERSON>(5000)", "primaryKey": false, "notNull": true}, "prescription_images": {"name": "prescription_images", "type": "<PERSON><PERSON><PERSON>(5000)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"refill_request_drug_details": {"name": "refill_request_drug_details", "columns": [{"expression": "drug_details", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"refill_request_service_order_id_fkey": {"name": "refill_request_service_order_id_fkey", "tableFrom": "refill_request", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["service_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.telehealth_service_state_mapping": {"name": "telehealth_service_state_mapping", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "state_id": {"name": "state_id", "type": "integer", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "service_type": {"name": "service_type", "type": "enum_telehealth_service_state_mapping_service_type", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"telehealth_service_state_mapping_state_id_fkey": {"name": "telehealth_service_state_mapping_state_id_fkey", "tableFrom": "telehealth_service_state_mapping", "tableTo": "states", "schemaTo": "public", "columnsFrom": ["state_id"], "columnsTo": ["state_id"], "onDelete": "no action", "onUpdate": "no action"}, "telehealth_service_state_mapping_service_id_fkey": {"name": "telehealth_service_state_mapping_service_id_fkey", "tableFrom": "telehealth_service_state_mapping", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.pharmacy_state_service_mapping": {"name": "pharmacy_state_service_mapping", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "state_id": {"name": "state_id", "type": "integer", "primaryKey": false, "notNull": false}, "pharmacy_id": {"name": "pharmacy_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"pharmacy_state_service_mapping_service_id_fkey": {"name": "pharmacy_state_service_mapping_service_id_fkey", "tableFrom": "pharmacy_state_service_mapping", "tableTo": "telehealth_services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pharmacy_state_service_mapping_state_id_fkey": {"name": "pharmacy_state_service_mapping_state_id_fkey", "tableFrom": "pharmacy_state_service_mapping", "tableTo": "states", "schemaTo": "public", "columnsFrom": ["state_id"], "columnsTo": ["state_id"], "onDelete": "no action", "onUpdate": "no action"}, "pharmacy_state_service_mapping_pharmacy_id_fkey": {"name": "pharmacy_state_service_mapping_pharmacy_id_fkey", "tableFrom": "pharmacy_state_service_mapping", "tableTo": "pharmacies", "schemaTo": "public", "columnsFrom": ["pharmacy_id"], "columnsTo": ["pharmacy_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.payment_details": {"name": "payment_details", "schema": "", "columns": {"payment_details_id": {"name": "payment_details_id", "type": "serial", "primaryKey": true, "notNull": true}, "payment_gateway": {"name": "payment_gateway", "type": "enum_payment_details_payment_gateway", "typeSchema": "public", "primaryKey": false, "notNull": true}, "payment_gateway_code": {"name": "payment_gateway_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payment_gateway_details": {"name": "payment_gateway_details", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "tennant_id": {"name": "tennant_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.states": {"name": "states", "schema": "", "columns": {"state_id": {"name": "state_id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "abbreviation": {"name": "abbreviation", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_async": {"name": "is_async", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.drugs_category": {"name": "drugs_category", "schema": "", "columns": {"category_id": {"name": "category_id", "type": "serial", "primaryKey": true, "notNull": true}, "category_name": {"name": "category_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.webhooks_log": {"name": "webhooks_log", "schema": "", "columns": {"webhooks_log_id": {"name": "webhooks_log_id", "type": "serial", "primaryKey": true, "notNull": true}, "body": {"name": "body", "type": "<PERSON><PERSON><PERSON>(5000)", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "response_status": {"name": "response_status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "case_id": {"name": "case_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "enum_webhooks_log_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "failed_message": {"name": "failed_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "headers": {"name": "headers", "type": "text", "primaryKey": false, "notNull": false}, "action_type": {"name": "action_type", "type": "enum_webhooks_log_action_type", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_insurance": {"name": "user_insurance", "schema": "", "columns": {"user_insurance_id": {"name": "user_insurance_id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "insurance_member_id": {"name": "insurance_member_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "insurance_plan_name": {"name": "insurance_plan_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "payer_identification": {"name": "payer_identification", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "cover_type": {"name": "cover_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_insurance_id_front": {"name": "user_insurance_id_front", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_insurance_id_back": {"name": "user_insurance_id_back", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_insurance_user_id_fkey": {"name": "user_insurance_user_id_fkey", "tableFrom": "user_insurance", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.insurance_eligibility_logs": {"name": "insurance_eligibility_logs", "schema": "", "columns": {"insurance_eligibility_log_id": {"name": "insurance_eligibility_log_id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": true}, "insurance_member_id": {"name": "insurance_member_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "full_response": {"name": "full_response", "type": "text", "primaryKey": false, "notNull": true}, "is_eligible": {"name": "is_eligible", "type": "boolean", "primaryKey": false, "notNull": true}, "ineligible_reason": {"name": "ineligible_reason", "type": "text", "primaryKey": false, "notNull": false}, "benefit_type": {"name": "benefit_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "benefit_amount": {"name": "benefit_amount", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "benefit_percentage": {"name": "benefit_percentage", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"insurance_eligibility_logs_order_id_fkey": {"name": "insurance_eligibility_logs_order_id_fkey", "tableFrom": "insurance_eligibility_logs", "tableTo": "telehealth_service_order", "schemaTo": "public", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_practice_groups": {"name": "user_practice_groups", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "practice_group_id": {"name": "practice_group_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_practice_groups_practice_group_id_fkey": {"name": "user_practice_groups_practice_group_id_fkey", "tableFrom": "user_practice_groups", "tableTo": "practice_groups", "schemaTo": "public", "columnsFrom": ["practice_group_id"], "columnsTo": ["practice_group_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_practice_groups_user_id_fkey": {"name": "user_practice_groups_user_id_fkey", "tableFrom": "user_practice_groups", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_practice_groups_pkey": {"name": "user_practice_groups_pkey", "columns": ["user_id", "practice_group_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.schedule_translation": {"name": "schedule_translation", "schema": "", "columns": {"schedule_id": {"name": "schedule_id", "type": "integer", "primaryKey": false, "notNull": true}, "language_code": {"name": "language_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "patient_history": {"name": "patient_history", "type": "text", "primaryKey": false, "notNull": false}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"schedule_translation_schedule_id_fkey": {"name": "schedule_translation_schedule_id_fkey", "tableFrom": "schedule_translation", "tableTo": "schedules", "schemaTo": "public", "columnsFrom": ["schedule_id"], "columnsTo": ["schedule_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"schedule_translation_pkey": {"name": "schedule_translation_pkey", "columns": ["schedule_id", "language_code"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.request_translation": {"name": "request_translation", "schema": "", "columns": {"request_id": {"name": "request_id", "type": "integer", "primaryKey": false, "notNull": true}, "language_code": {"name": "language_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "patient_history": {"name": "patient_history", "type": "text", "primaryKey": false, "notNull": false}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"request_translation_request_id_fkey": {"name": "request_translation_request_id_fkey", "tableFrom": "request_translation", "tableTo": "requests", "schemaTo": "public", "columnsFrom": ["request_id"], "columnsTo": ["request_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"request_translation_pkey": {"name": "request_translation_pkey", "columns": ["request_id", "language_code"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users_translation": {"name": "users_translation", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "language_code": {"name": "language_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "history": {"name": "history", "type": "text", "primaryKey": false, "notNull": false}, "questionnaire": {"name": "questionnaire", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_translation_user_id_fkey": {"name": "users_translation_user_id_fkey", "tableFrom": "users_translation", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"users_translation_pkey": {"name": "users_translation_pkey", "columns": ["user_id", "language_code"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_viewers": {"name": "user_viewers", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "viewer_id": {"name": "viewer_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_favorite": {"name": "user_favorite", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "viewer_favorite": {"name": "viewer_favorite", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_viewers_user_id_fkey": {"name": "user_viewers_user_id_fkey", "tableFrom": "user_viewers", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_viewers_viewer_id_fkey": {"name": "user_viewers_viewer_id_fkey", "tableFrom": "user_viewers", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["viewer_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_viewers_pkey": {"name": "user_viewers_pkey", "columns": ["user_id", "viewer_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_associations": {"name": "user_associations", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "buser_id": {"name": "buser_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_favorite": {"name": "user_favorite", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "buser_favorite": {"name": "buser_favorite", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "ma_manage_orders": {"name": "ma_manage_orders", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_notify_on_capture": {"name": "is_notify_on_capture", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_rpm_enabled": {"name": "is_rpm_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_customized_vitals_thresholds": {"name": "is_customized_vitals_thresholds", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "vitals_thresholds": {"name": "vitals_thresholds", "type": "text", "primaryKey": false, "notNull": false}, "vitals_cron": {"name": "vitals_cron", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_associations_buser_id_fkey": {"name": "user_associations_buser_id_fkey", "tableFrom": "user_associations", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["buser_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "user_associations_user_id_fkey": {"name": "user_associations_user_id_fkey", "tableFrom": "user_associations", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_associations_pkey": {"name": "user_associations_pkey", "columns": ["user_id", "buser_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.enum_consult_order_files_type": {"name": "enum_consult_order_files_type", "values": ["audio", "video", "file", "image"], "schema": "public"}, "public.enum_follow_up_reminder_status": {"name": "enum_follow_up_reminder_status", "values": ["pending", "started", "failed", "sent", "cancel", "cancelled_by_admin"], "schema": "public"}, "public.enum_jobs_status": {"name": "enum_jobs_status", "values": ["pending", "started", "failed", "sending_fax", "completed"], "schema": "public"}, "public.enum_lifefile_configuration_shipping_services": {"name": "enum_lifefile_configuration_shipping_services", "values": ["7780", "9"], "schema": "public"}, "public.enum_onehealth_lab_orders_payment_status": {"name": "enum_onehealth_lab_orders_payment_status", "values": ["PENDING", "FAILED", "PAID"], "schema": "public"}, "public.enum_onehealth_lab_orders_status": {"name": "enum_onehealth_lab_orders_status", "values": ["PENDING", "PAYMENT_PENDING", "COMPLETE_COLLECTION", "COMPLETED"], "schema": "public"}, "public.enum_orders_category": {"name": "enum_orders_category", "values": ["CCM", "RPM", "BHI"], "schema": "public"}, "public.enum_orders_status": {"name": "enum_orders_status", "values": ["STARTED", "INPROGRESS", "ENDED", "NOANSWER", "REJECTED", "QUEUED", "RINGING", "CANCELLED", "COMPLETED", "BUSY", "FAILED"], "schema": "public"}, "public.enum_orders_type": {"name": "enum_orders_type", "values": ["audio", "video", "one-way", "online", "voip"], "schema": "public"}, "public.enum_payment_details_payment_gateway": {"name": "enum_payment_details_payment_gateway", "values": ["paypal", "stripe", "recurly", "authorize_net"], "schema": "public"}, "public.enum_prescription_preference_preference": {"name": "enum_prescription_preference_preference", "values": ["fax", "life_file", "doespot"], "schema": "public"}, "public.enum_prescription_transfer_medications_status": {"name": "enum_prescription_transfer_medications_status", "values": ["pending", "transferred"], "schema": "public"}, "public.enum_promo_codes_code_type": {"name": "enum_promo_codes_code_type", "values": ["PROMO CODE", "INVITE PROMO CODE", "MD", "LIFESTYLE", "LAB", "MD/LIFESTYLE/LAB"], "schema": "public"}, "public.enum_promo_codes_discount_type": {"name": "enum_promo_codes_discount_type", "values": ["PERCENTAGE", "DOLLAR"], "schema": "public"}, "public.enum_promo_codes_usage_type": {"name": "enum_promo_codes_usage_type", "values": ["SINGLE", "MULTIPLE"], "schema": "public"}, "public.enum_referral_tracking_shipping_partner": {"name": "enum_referral_tracking_shipping_partner", "values": ["UPS", "USPS", "FedEx"], "schema": "public"}, "public.enum_referrals_status": {"name": "enum_referrals_status", "values": ["issued", "canceled", "pdf_pending"], "schema": "public"}, "public.enum_requests_status": {"name": "enum_requests_status", "values": ["open", "accepted", "rejected"], "schema": "public"}, "public.enum_service_payment_mapping_payment_type": {"name": "enum_service_payment_mapping_payment_type", "values": ["subscription", "one-time", "one-time-insurance"], "schema": "public"}, "public.enum_stripe_user_details_cc_status": {"name": "enum_stripe_user_details_cc_status", "values": ["captured", "not_captured", "payment_error", "capture_immediate"], "schema": "public"}, "public.enum_stripe_user_details_oauth_status": {"name": "enum_stripe_user_details_oauth_status", "values": ["connected", "not_connected", "payouts_disabled"], "schema": "public"}, "public.enum_stripe_user_payment_details_payment_status": {"name": "enum_stripe_user_payment_details_payment_status", "values": ["success", "failed", "action_required"], "schema": "public"}, "public.enum_subscription_plans_billing_cycle": {"name": "enum_subscription_plans_billing_cycle", "values": ["monthly", "yearly", "weekly"], "schema": "public"}, "public.enum_telehealth_service_order_claim_type": {"name": "enum_telehealth_service_order_claim_type", "values": ["AUTO", "WORK"], "schema": "public"}, "public.enum_telehealth_service_order_service_type": {"name": "enum_telehealth_service_order_service_type", "values": ["SYNC", "ASYNC"], "schema": "public"}, "public.enum_telehealth_service_order_status": {"name": "enum_telehealth_service_order_status", "values": ["pending", "accept", "completed", "errored", "cancelled", "patient_verification_pending", "archive", "cancelled_by_provider", "LabRequested", "LabReceived", "schedule_pending", "lab_approval_pending", "lab_results_approved", "lab_results_denied", "clinical_denial", "cancelled_by_patient", "now_show", "no_show", "payment_pending", "pharmacy_pending", "questionnaire_pending"], "schema": "public"}, "public.enum_telehealth_service_order_visit_type": {"name": "enum_telehealth_service_order_visit_type", "values": ["IN_PERSON", "ONLINE"], "schema": "public"}, "public.enum_telehealth_service_questions_question_for": {"name": "enum_telehealth_service_questions_question_for", "values": ["male", "female", "both"], "schema": "public"}, "public.enum_telehealth_service_questions_question_type": {"name": "enum_telehealth_service_questions_question_type", "values": ["YesNo", "Text", "Selection", "MultipleSelection", "Date", "DateTime", "TextArea", "Height", "Weight", "Bmi", "FileUpload", "HeightWeightBmi"], "schema": "public"}, "public.enum_telehealth_service_state_mapping_service_type": {"name": "enum_telehealth_service_state_mapping_service_type", "values": ["SYNC", "ASYNC"], "schema": "public"}, "public.enum_telehealth_services_service_mode": {"name": "enum_telehealth_services_service_mode", "values": ["SYNC", "ASYNC", "BOTH_SYNC_ASYNC"], "schema": "public"}, "public.enum_telehealth_services_service_type": {"name": "enum_telehealth_services_service_type", "values": ["BUSER", "AUSER", "medical_assistant", "pharmacist", "DIETICIAN_NUTRITION", "MENTAL_HEALTH", "WEIGHT_LOSS_MANAGEMENT", "DIABETES_PREVENTION"], "schema": "public"}, "public.enum_tennant_master_preferred_payment_gateway": {"name": "enum_tennant_master_preferred_payment_gateway", "values": ["PAYPAL", "STRIPE"], "schema": "public"}, "public.enum_transactions_payment_method_type": {"name": "enum_transactions_payment_method_type", "values": ["STRIPE", "BRAINTREE", "PAYPAL"], "schema": "public"}, "public.enum_transactions_payment_status": {"name": "enum_transactions_payment_status", "values": ["pending", "completed", "errored", "cancelled"], "schema": "public"}, "public.enum_transactions_refund_payment_status": {"name": "enum_transactions_refund_payment_status", "values": ["succeeded", "failed", "pending", "n/a"], "schema": "public"}, "public.enum_transactions_status": {"name": "enum_transactions_status", "values": ["succeeded", "failed", "pending"], "schema": "public"}, "public.enum_transactions_transaction_status": {"name": "enum_transactions_transaction_status", "values": ["initiated", "completed"], "schema": "public"}, "public.enum_user_diet_meal_type": {"name": "enum_user_diet_meal_type", "values": ["breakfast", "lunch", "dinner", "snacks"], "schema": "public"}, "public.enum_user_file_repo_details_upload_type": {"name": "enum_user_file_repo_details_upload_type", "values": ["FAX", "SFTP"], "schema": "public"}, "public.enum_user_vitals_mode": {"name": "enum_user_vitals_mode", "values": ["automated", "doctor", "patient"], "schema": "public"}, "public.enum_users_gender": {"name": "enum_users_gender", "values": ["male", "female", "others", "transgender-female", "transgender-male"], "schema": "public"}, "public.enum_users_role": {"name": "enum_users_role", "values": ["USER", "BUSER", "AUSER", "medical_assistant", "viewer", "support_user", "pharmacist", "PHARMACY", "GROUP_ADMIN", "SUPPORT_ADMIN", "DEVELOPER"], "schema": "public"}, "public.enum_users_status": {"name": "enum_users_status", "values": ["AVAILABLE", "BUSY", "AWAY", "OFFLINE", "ACTIVATION_PENDING", "ONBOARDING_PENDING", "PROFILE_INCOMPLETE"], "schema": "public"}, "public.enum_users_sub_role": {"name": "enum_users_sub_role", "values": ["USER", "DIETICIAN_NUTRITION", "MENTAL_HEALTH", "WEIGHT_LOSS_MANAGEMENT", "DIABETES_PREVENTION"], "schema": "public"}, "public.enum_visit_summary_upload_status_upload_status": {"name": "enum_visit_summary_upload_status_upload_status", "values": ["SUCCESS", "FAILED"], "schema": "public"}, "public.enum_visit_summary_upload_status_upload_type": {"name": "enum_visit_summary_upload_status_upload_type", "values": ["FAX", "SFTP"], "schema": "public"}, "public.enum_vitals_summary_upload_status_upload_status": {"name": "enum_vitals_summary_upload_status_upload_status", "values": ["SUCCESS", "FAILED"], "schema": "public"}, "public.enum_vitals_summary_upload_status_upload_type": {"name": "enum_vitals_summary_upload_status_upload_type", "values": ["FAX", "SFTP"], "schema": "public"}, "public.enum_webhooks_log_action_type": {"name": "enum_webhooks_log_action_type", "values": ["webhook", "prescription_api"], "schema": "public"}, "public.enum_webhooks_log_status": {"name": "enum_webhooks_log_status", "values": ["pending", "success", "failed"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {"public.dashboard_tennant_list": {"name": "dashboard_tennant_list", "schema": "public", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": false, "notNull": false}, "tennant_name": {"name": "tennant_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "async_count": {"name": "async_count", "type": "bigint", "primaryKey": false, "notNull": false}, "sync_count": {"name": "sync_count", "type": "bigint", "primaryKey": false, "notNull": false}, "total_orders": {"name": "total_orders", "type": "bigint", "primaryKey": false, "notNull": false}}, "isExisting": false, "definition": "SELECT tennant_master.id, tennant_master.tennant_name, sum( CASE WHEN telehealth_service_order.service_type = 'ASYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS async_count, sum( CASE WHEN telehealth_service_order.service_type = 'SYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS sync_count, count(telehealth_service_order.id) AS total_orders FROM tennant_master JOIN users ON tennant_master.id = users.tennant_id JOIN telehealth_service_order ON users.user_id = telehealth_service_order.answer_given_by GROUP BY tennant_master.id, tennant_master.tennant_name ORDER BY tennant_master.id", "materialized": false}}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {"consult_notes": {"columns": {"general_note": {"isDefaultAnExpression": true}}}, "requests": {"columns": {"order_guid": {"isDefaultAnExpression": true}}}, "onehealth_lab_orders": {"columns": {"lab_id": {"isDefaultAnExpression": true}}}, "orders": {"columns": {"order_guid": {"isDefaultAnExpression": true}}}, "transactions": {"columns": {"order_guid": {"isDefaultAnExpression": true}}}, "chat_messages": {"columns": {"read_by": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "users": {"columns": {"my_invite_code": {"isDefaultAnExpression": true}, "referred_by_invite_code": {"isDefaultAnExpression": true}}}, "telehealth_services": {"columns": {"display_amount": {"isDefaultAnExpression": true}}}, "schedules": {"columns": {"order_guid": {"isDefaultAnExpression": true}}}}}}