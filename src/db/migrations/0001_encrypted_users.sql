-- CREATE TABLE encrypted_users (
--   id SERIAL PRIMARY KEY,
--   email VARCHAR(255) NOT NULL UNIQUE,
--   first_name BY<PERSON><PERSON>,  -- Encrypted using PGP_SYM_ENCRYPT
--   last_name BY<PERSON><PERSON>,   -- Encrypted using PGP_SYM_ENCRYPT
--   created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
--   updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
-- );

-- -- Create trigger to update updated_at timestamp
-- CREATE OR REPLACE FUNCTION update_updated_at_column()
-- RETURNS TRIGGER AS $$
-- BEGIN
--   NEW.updated_at = CURRENT_TIMESTAMP;
--   RETURN NEW;
-- END;
-- $$ language 'plpgsql';

-- CREATE TRIGGER update_encrypted_users_updated_at
--   BEFORE UPDATE ON encrypted_users
--   FOR <PERSON>CH ROW
--   EXECUTE FUNCTION update_updated_at_column();
