import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const externalRequestsLog = pgTable(
  'external_requests_log',
  {
    externalRequestLogId: serial('external_request_log_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id'),
    type: varchar({ length: 255 }).notNull(),
    detail: text().notNull(),
    timestamp: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'external_requests_log_user_id_fkey'
    })
  ]
);

export const externalRequestsLogRelations = relations(
  externalRequestsLog,
  ({ one }) => ({
    user: one(users, {
      fields: [externalRequestsLog.userId],
      references: [users.userId]
    })
  })
);
