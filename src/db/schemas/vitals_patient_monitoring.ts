import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  boolean,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const vitalsPatientMonitoring = pgTable(
  'vitals_patient_monitoring',
  {
    patientId: integer('patient_id').notNull(),
    buserId: integer('buser_id').notNull(),
    groupId: serial('group_id').primaryKey().notNull(),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    billed: boolean().default(false),
    billedDate: timestamp('billed_date', {
      withTimezone: true,
      mode: 'string'
    }),
    cost: numeric(),
    currency: text(),
    procedure: text().default(
      '{"CPT_codes":[{"code":"99454","description":"Device(s) supply with daily recording(s) or programmed alert(s) transmission, each 30 days"}]}'
    ),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.buserId],
      foreignColumns: [users.userId],
      name: 'vitals_patient_monitoring_buser_id_fkey'
    }),
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [users.userId],
      name: 'vitals_patient_monitoring_patient_id_fkey'
    })
  ]
);

export const vitalsPatientMonitoringRelations = relations(
  vitalsPatientMonitoring,
  ({ one }) => ({
    user_buserId: one(users, {
      fields: [vitalsPatientMonitoring.buserId],
      references: [users.userId],
      relationName: 'vitalsPatientMonitoring_buserId_users_userId'
    }),
    user_patientId: one(users, {
      fields: [vitalsPatientMonitoring.patientId],
      references: [users.userId],
      relationName: 'vitalsPatientMonitoring_patientId_users_userId'
    })
  })
);
