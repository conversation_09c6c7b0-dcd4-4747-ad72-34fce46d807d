import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { referrals } from './referrals';
import { telehealthServiceOrder } from './telehealth_service_order';
export const enumJobsStatus = pgEnum('enum_jobs_status', [
  'pending',
  'started',
  'failed',
  'sending_fax',
  'completed'
]);
export const jobs = pgTable(
  'jobs',
  {
    id: serial().primaryKey().notNull(),
    path: text(),
    orderId: integer('order_id').notNull(),
    referralId: integer('referral_id').notNull(),
    status: enumJobsStatus().default('pending'),
    failedMessage: text('failed_message'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'jobs_order_id_fkey'
    }),
    foreignKey({
      columns: [table.referralId],
      foreignColumns: [referrals.referralId],
      name: 'jobs_referral_id_fkey'
    })
  ]
);

export const jobsRelations = relations(jobs, ({ one }) => ({
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [jobs.orderId],
    references: [telehealthServiceOrder.id]
  }),
  referral: one(referrals, {
    fields: [jobs.referralId],
    references: [referrals.referralId]
  })
}));
