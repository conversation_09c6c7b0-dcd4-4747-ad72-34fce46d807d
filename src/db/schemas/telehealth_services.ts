import { sql, relations } from 'drizzle-orm';
import {
  text,
  uuid,
  json,
  serial,
  unique,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { emailTemplate } from './email_template';
import { medicineServicePharmacyMapping } from './medicine_service_pharmacy_mapping';
import { pharmacyStateServiceMapping } from './pharmacy_state_service_mapping';
import { promoCodes } from './promo_codes';
import { serviceActionPreference } from './service_action_preference';
import { smsTemplate } from './sms_template';
import { telehealthServiceMaster } from './telehealth_service_master';
import { telehealthServiceOrder } from './telehealth_service_order';
import { telehealthServiceProcedureCodesMapping } from './telehealth_service_procedure_codes_mapping';
import { telehealthServiceProviderMapping } from './telehealth_service_provider_mapping';
import { telehealthServiceQuestions } from './telehealth_service_questions';
import { telehealthServiceStateMapping } from './telehealth_service_state_mapping';
import { tenantFiles } from './tenant_files';

export const enumTelehealthServicesServiceMode = pgEnum(
  'enum_telehealth_services_service_mode',
  ['SYNC', 'ASYNC', 'BOTH_SYNC_ASYNC']
);
export const enumTelehealthServicesServiceType = pgEnum(
  'enum_telehealth_services_service_type',
  [
    'BUSER',
    'AUSER',
    'medical_assistant',
    'pharmacist',
    'DIETICIAN_NUTRITION',
    'MENTAL_HEALTH',
    'WEIGHT_LOSS_MANAGEMENT',
    'DIABETES_PREVENTION'
  ]
);

export const telehealthServices: any = pgTable(
  'telehealth_services',
  {
    id: serial().primaryKey().notNull(),
    serviceName: varchar('service_name', { length: 200 }).notNull(),
    description: varchar({ length: 2000 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    serviceType:
      enumTelehealthServicesServiceType('service_type').default('BUSER'),
    displayOrder: integer('display_order').default(1),
    serviceMode:
      enumTelehealthServicesServiceMode('service_mode').default(
        'BOTH_SYNC_ASYNC'
      ),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    amount: integer(),
    tenantId: integer('tenant_id'),
    paypalPlanId: text('paypal_plan_id'),
    title: varchar({ length: 255 }),
    subtitle: varchar({ length: 2000 }),
    serviceKey: varchar('service_key', { length: 255 }),
    sessionType: varchar('session_type', { length: 255 }),
    fieldsOptions: varchar('fields_options', { length: 255 }),
    onCompleteScript: varchar('on_complete_script', { length: 2000 }),
    onFollowUpScript: varchar('on_follow_up_script', { length: 2000 }),
    accessLabsTestCode: varchar('access_labs_test_code', { length: 2000 }),
    onCreateScript: varchar('on_create_script', { length: 2000 }),
    onLabResultReceivedScript: varchar('on_lab_result_received_script', {
      length: 2000
    }),
    onUpdateScheduleScript: varchar('on_update_schedule_script', {
      length: 2000
    }),
    disclaimer: text(),
    serviceDetails: text('service_details'),
    isVideoCall: boolean('is_video_call').default(true).notNull(),
    isAudioCall: boolean('is_audio_call').default(true).notNull(),
    displayQuestionnaire: boolean('display_questionnaire')
      .default(false)
      .notNull(),
    displayServiceName: text('display_service_name'),
    erxDrugKey: varchar('erx_drug_key', { length: 255 }),
    filterDisplay: boolean('filter_display').default(false).notNull(),
    servicesGuid: uuid('services_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    originalAmount: varchar('original_amount', { length: 255 }),
    durationText: varchar('duration_text', { length: 255 }),
    discountText: varchar('discount_text', { length: 255 }),
    consultInstruction: json('consult_instruction'),
    serviceInstruction: json('service_instruction'),
    serviceMasterId: integer('service_master_id'),
    nextServiceId: integer('next_service_id'),
    eligibleMessages: json('eligible_messages'),
    displayAmount: varchar('display_amount', { length: 255 }).default(
      sql`NULL`
    ),
    userConsent: text('user_consent'),
    preferredPharmacySelection: varchar('preferred_pharmacy_selection', {
      length: 255
    }),
    nextFollowupDays: integer('next_followup_days')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'telehealth_services_service_master_id_fkey'
    }),
    foreignKey({
      columns: [table.nextServiceId],
      foreignColumns: [table.id],
      name: 'telehealth_services_next_service_id_fkey'
    }),
    unique('telehealth_services_service_name_key').on(table.serviceName)
  ]
);

export const telehealthServicesRelations = relations(
  telehealthServices,
  ({ one, many }) => ({
    promoCodes: many(promoCodes),
    telehealthServiceOrders: many(telehealthServiceOrder),
    medicineServicePharmacyMappings: many(medicineServicePharmacyMapping),
    telehealthServiceProcedureCodesMappings: many(
      telehealthServiceProcedureCodesMapping
    ),
    telehealthServiceQuestions: many(telehealthServiceQuestions),
    telehealthServiceProviderMappings: many(telehealthServiceProviderMapping),
    smsTemplates: many(smsTemplate),
    emailTemplates: many(emailTemplate),
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [telehealthServices.serviceMasterId],
      references: [telehealthServiceMaster.id],
      relationName:
        'telehealthServices_serviceMasterId_telehealthServiceMaster_id'
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServices.nextServiceId],
      references: [telehealthServices.id],
      relationName: 'telehealthServices_nextServiceId_telehealthServices_id'
    }),
    telehealthServices: many(telehealthServices, {
      relationName: 'telehealthServices_nextServiceId_telehealthServices_id'
    }),
    telehealthServiceMasters: many(telehealthServiceMaster, {
      relationName:
        'telehealthServiceMaster_initialServiceId_telehealthServices_id'
    }),
    serviceActionPreferences: many(serviceActionPreference),
    telehealthServiceStateMappings: many(telehealthServiceStateMapping),
    pharmacyStateServiceMappings: many(pharmacyStateServiceMapping),
    tenantFiles: many(tenantFiles)
  })
);
