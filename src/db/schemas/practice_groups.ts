import { relations } from 'drizzle-orm';
import { text, serial, pgTable, varchar, timestamp } from 'drizzle-orm/pg-core';

import { userPracticeGroups } from './user_practice_groups';

export const practiceGroups = pgTable('practice_groups', {
  practiceGroupId: serial('practice_group_id').primaryKey().notNull(),
  name: text(),
  phones: text(),
  visitAddress: text('visit_address'),
  installType: varchar('install_type', { length: 255 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const practiceGroupsRelations = relations(
  practiceGroups,
  ({ many }) => ({
    userPracticeGroups: many(userPracticeGroups)
  })
);
