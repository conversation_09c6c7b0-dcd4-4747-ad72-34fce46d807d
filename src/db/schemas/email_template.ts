import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServices } from './telehealth_services';

export const emailTemplate = pgTable(
  'email_template',
  {
    emailTemplateId: serial('email_template_id').primaryKey().notNull(),
    emailAction: varchar('email_action', { length: 100 }).notNull(),
    emailTemplateName: varchar('email_template_name', {
      length: 500
    }).notNull(),
    serviceId: integer('service_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'email_template_service_id_fkey'
    })
  ]
);

export const emailTemplateRelations = relations(emailTemplate, ({ one }) => ({
  telehealthService: one(telehealthServices, {
    fields: [emailTemplate.serviceId],
    references: [telehealthServices.id]
  })
}));
