import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { mmsPatients } from './mms_patients';

export const patientInsurances = pgTable(
  'patient_insurances',
  {
    patientInsuranceId: serial('patient_insurance_id').primaryKey().notNull(),
    patientId: integer('patient_id').notNull(),
    insuranceId: varchar('insurance_id', { length: 255 }),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [mmsPatients.patientId],
      name: 'patient_insurances_patient_id_fkey'
    })
  ]
);

export const patientInsurancesRelations = relations(
  patientInsurances,
  ({ one }) => ({
    mmsPatient: one(mmsPatients, {
      fields: [patientInsurances.patientId],
      references: [mmsPatients.patientId]
    })
  })
);
