import { relations } from 'drizzle-orm';
import { pgTable, integer, timestamp, foreignKey } from 'drizzle-orm/pg-core';

import { users } from './users';

export const vitalsNotificationCycle = pgTable(
  'vitals_notification_cycle',
  {
    userId: integer('user_id').primaryKey().notNull(),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'vitals_notification_cycle_user_id_fkey'
    })
  ]
);

export const vitalsNotificationCycleRelations = relations(
  vitalsNotificationCycle,
  ({ one }) => ({
    user: one(users, {
      fields: [vitalsNotificationCycle.userId],
      references: [users.userId]
    })
  })
);
