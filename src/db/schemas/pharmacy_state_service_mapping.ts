import { sql, relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { pharmacies } from './pharmacies';
import { states } from './states';
import { telehealthServices } from './telehealth_services';

export const pharmacyStateServiceMapping = pgTable(
  'pharmacy_state_service_mapping',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    stateId: integer('state_id'),
    pharmacyId: integer('pharmacy_id'),
    status: boolean().default(true).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'pharmacy_state_service_mapping_service_id_fkey'
    }),
    foreignKey({
      columns: [table.stateId],
      foreignColumns: [states.stateId],
      name: 'pharmacy_state_service_mapping_state_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'pharmacy_state_service_mapping_pharmacy_id_fkey'
    })
  ]
);

export const pharmacyStateServiceMappingRelations = relations(
  pharmacyStateServiceMapping,
  ({ one }) => ({
    telehealthService: one(telehealthServices, {
      fields: [pharmacyStateServiceMapping.serviceId],
      references: [telehealthServices.id]
    }),
    state: one(states, {
      fields: [pharmacyStateServiceMapping.stateId],
      references: [states.stateId]
    }),
    pharmacy: one(pharmacies, {
      fields: [pharmacyStateServiceMapping.pharmacyId],
      references: [pharmacies.pharmacyId]
    })
  })
);
