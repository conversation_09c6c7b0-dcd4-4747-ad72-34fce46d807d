import { relations } from 'drizzle-orm';
import {
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { paymentDetails } from './payment_details';
import { telehealthServiceMaster } from './telehealth_service_master';

export const enumServicePaymentMappingPayment = pgEnum(
  'enum_service_payment_mapping_payment_type',
  ['subscription', 'one-time', 'one-time-insurance', 'no_payment']
);

export const servicePaymentMapping = pgTable(
  'service_payment_mapping',
  {
    servicePaymentMappingId: serial('service_payment_mapping_id')
      .primaryKey()
      .notNull(),
    serviceKey: varchar('service_key', { length: 255 }),
    serviceMasterId: integer('service_master_id'),
    paymentDetailsId: integer('payment_details_id').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
    // paymentType:
    //   enumServicePaymentMappingPaymentType('payment_type').default(
    //     'subscription'
    //   )
  },
  (table) => [
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'service_payment_mapping_service_master_id_fkey'
    }),
    foreignKey({
      columns: [table.paymentDetailsId],
      foreignColumns: [paymentDetails.paymentDetailsId],
      name: 'service_payment_mapping_payment_details_id_fkey'
    })
  ]
);

export const servicePaymentMappingRelations = relations(
  servicePaymentMapping,
  ({ one }) => ({
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [servicePaymentMapping.serviceMasterId],
      references: [telehealthServiceMaster.id]
    }),
    paymentDetail: one(paymentDetails, {
      fields: [servicePaymentMapping.paymentDetailsId],
      references: [paymentDetails.paymentDetailsId]
    })
  })
);
