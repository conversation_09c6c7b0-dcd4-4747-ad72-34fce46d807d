import { text, serial, pgTable, varchar, timestamp } from 'drizzle-orm/pg-core';

import type { InferModel } from 'drizzle-orm';

export const encryptedUsers = pgTable('encrypted_users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  firstName: text('first_name'),
  lastName: text('last_name'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow()
});

export type EncryptedUser = InferModel<typeof encryptedUsers>;
export type NewEncryptedUser = InferModel<typeof encryptedUsers, 'insert'>;
