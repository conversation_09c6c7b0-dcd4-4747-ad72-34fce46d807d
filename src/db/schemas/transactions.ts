import { sql, relations } from 'drizzle-orm';
import {
  text,
  pgEnum,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey,
  doublePrecision
} from 'drizzle-orm/pg-core';

import { paymentDetails } from './payment_details';
import { users } from './users';
export const enumTransactionsStatus = pgEnum('enum_transactions_status', [
  'succeeded',
  'failed',
  'pending'
]);
export const enumTransactionsPaymentMethodType = pgEnum(
  'enum_transactions_payment_method_type',
  ['STRIPE', 'BRAINTREE', 'PAYPAL', 'RECURLY', 'AUTHORISED_NET', 'NMI']
);
export const enumTransactionsPaymentStatus = pgEnum(
  'enum_transactions_payment_status',
  ['pending', 'completed', 'errored', 'cancelled', 'RECURLY', 'AUTHORISED_NET']
);
export const enumTransactionsRefundPaymentStatus = pgEnum(
  'enum_transactions_refund_payment_status',
  ['succeeded', 'failed', 'pending', 'n/a']
);

export const enumTransactionsTransactionStatus = pgEnum(
  'enum_transactions_transaction_status',
  ['initiated', 'completed']
);
export const transactions = pgTable(
  'transactions',
  {
    transactionId: text('transaction_id').primaryKey().notNull(),
    payerUserId: integer('payer_user_id').notNull(),
    payeeUserId: integer('payee_user_id'),
    amount: doublePrecision().notNull(),
    currency: text().notNull(),
    status: enumTransactionsStatus().notNull(),
    description: text(),
    errorDescription: text('error_description'),
    cardDetails: text('card_details'),
    lineItems: text('line_items'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    globalId: varchar('global_id', { length: 100 }),
    refundTransactionId: varchar('refund_transaction_id', { length: 100 }),
    refundCreatedAt: timestamp('refund_created_at', {
      withTimezone: true,
      mode: 'string'
    }),
    refundGlobalId: varchar('refund_global_id', { length: 100 }),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    paymentMethodType: enumTransactionsPaymentMethodType(
      'payment_method_type'
    ).default('BRAINTREE'),
    refundPaymentStatus: enumTransactionsRefundPaymentStatus(
      'refund_payment_status'
    ).default('n/a'),
    refundPaymentAmount: doublePrecision('refund_payment_amount').default(0),
    refundErrorDescription: text('refund_error_description'),
    refundSuccessResponse: text('refund_success_response'),
    transactionStatus: varchar('transaction_status', { length: 100 }),
    paymentDetailsId: integer('payment_details_id')
  },
  (table) => [
    foreignKey({
      columns: [table.payeeUserId],
      foreignColumns: [users.userId],
      name: 'transactions_payee_user_id_fkey'
    }),
    foreignKey({
      columns: [table.payerUserId],
      foreignColumns: [users.userId],
      name: 'transactions_payer_user_id_fkey'
    }),
    foreignKey({
      columns: [table.paymentDetailsId],
      foreignColumns: [paymentDetails.paymentDetailsId],
      name: 'transactions_payment_details_id_fkey'
    })
  ]
);

export const transactionsRelations = relations(transactions, ({ one }) => ({
  user_payeeUserId: one(users, {
    fields: [transactions.payeeUserId],
    references: [users.userId],
    relationName: 'transactions_payeeUserId_users_userId'
  }),
  user_payerUserId: one(users, {
    fields: [transactions.payerUserId],
    references: [users.userId],
    relationName: 'transactions_payerUserId_users_userId'
  }),
  paymentDetail: one(paymentDetails, {
    fields: [transactions.paymentDetailsId],
    references: [paymentDetails.paymentDetailsId]
  })
}));
