import { relations } from 'drizzle-orm';
import {
  json,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServices } from './telehealth_services';
import { tennantMaster } from './tennant_master';

export const tenantFiles = pgTable(
  'tenant_files',
  {
    id: serial().primaryKey().notNull(),
    tenantId: integer('tenant_id').notNull(),
    serviceId: integer('service_id'),
    key: varchar({ length: 255 }),
    disclaimerFile: json('disclaimer_file'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'tenant_files_tenant_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'tenant_files_service_id_fkey'
    })
  ]
);

export const tenantFilesRelations = relations(tenantFiles, ({ one }) => ({
  tennantMaster: one(tennantMaster, {
    fields: [tenantFiles.tenantId],
    references: [tennantMaster.id]
  }),
  telehealthService: one(telehealthServices, {
    fields: [tenantFiles.serviceId],
    references: [telehealthServices.id]
  })
}));
