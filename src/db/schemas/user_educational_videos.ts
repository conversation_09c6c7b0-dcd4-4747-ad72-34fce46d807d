import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { educationalVideos } from './educational_videos';
import { users } from './users';

export const userEducationalVideos = pgTable(
  'user_educational_videos',
  {
    userVideoId: serial('user_video_id').primaryKey().notNull(),
    videoId: serial('video_id').notNull(),
    referredBy: integer('referred_by').notNull(),
    referredFor: integer('referred_for').notNull(),
    doctorId: integer('doctor_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    viewedAt: timestamp('viewed_at', { withTimezone: true, mode: 'string' }),
    viewed: boolean()
  },
  (table) => [
    foreignKey({
      columns: [table.referredBy],
      foreignColumns: [users.userId],
      name: 'user_educational_videos_assigned_by_fkey'
    }),
    foreignKey({
      columns: [table.referredFor],
      foreignColumns: [users.userId],
      name: 'user_educational_videos_assigned_to_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_educational_videos_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.videoId],
      foreignColumns: [educationalVideos.videoId],
      name: 'user_educational_videos_video_id_fkey'
    })
  ]
);

export const userEducationalVideosRelations = relations(
  userEducationalVideos,
  ({ one }) => ({
    user_referredBy: one(users, {
      fields: [userEducationalVideos.referredBy],
      references: [users.userId],
      relationName: 'userEducationalVideos_referredBy_users_userId'
    }),
    user_referredFor: one(users, {
      fields: [userEducationalVideos.referredFor],
      references: [users.userId],
      relationName: 'userEducationalVideos_referredFor_users_userId'
    }),
    user_doctorId: one(users, {
      fields: [userEducationalVideos.doctorId],
      references: [users.userId],
      relationName: 'userEducationalVideos_doctorId_users_userId'
    }),
    educationalVideo: one(educationalVideos, {
      fields: [userEducationalVideos.videoId],
      references: [educationalVideos.videoId]
    })
  })
);
