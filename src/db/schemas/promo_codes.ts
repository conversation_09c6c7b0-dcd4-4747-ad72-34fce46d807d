import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServices } from './telehealth_services';

export const enumPromoCodesCodeType = pgEnum('enum_promo_codes_code_type', [
  'PROMO CODE',
  'INVITE PROMO CODE',
  'MD',
  'LIFESTYLE',
  'LAB',
  'MD/LIFESTYLE/LAB'
]);
export const enumPromoCodesDiscountType = pgEnum(
  'enum_promo_codes_discount_type',
  ['PERCENTAGE', 'DOLLAR']
);
export const enumPromoCodesUsageType = pgEnum('enum_promo_codes_usage_type', [
  'SINGLE',
  'MULTIPLE'
]);

export const promoCodes = pgTable(
  'promo_codes',
  {
    codeId: serial('code_id').primaryKey().notNull(),
    codeType: enumPromoCodesCodeType('code_type').notNull(),
    serviceId: integer('service_id'),
    labId: integer('lab_id'),
    discountType: enumPromoCodesDiscountType('discount_type'),
    discountValues: text('discount_values'),
    promoCode: text('promo_code'),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    usageType: enumPromoCodesUsageType('usage_type'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    isDeleted: boolean('is_deleted').default(false),
    createdBy: integer('created_by'),
    updatedBy: integer('updated_by'),
    deletedBy: integer('deleted_by')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'promo_codes_service_id_fkey'
    })
  ]
);

export const promoCodesRelations = relations(promoCodes, ({ one }) => ({
  telehealthService: one(telehealthServices, {
    fields: [promoCodes.serviceId],
    references: [telehealthServices.id]
  })
}));
