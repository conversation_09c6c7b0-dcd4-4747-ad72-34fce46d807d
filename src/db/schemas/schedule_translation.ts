import { relations } from 'drizzle-orm';
import {
  text,
  pgTable,
  varchar,
  integer,
  foreignKey,
  primaryKey
} from 'drizzle-orm/pg-core';

import { schedules } from './schedules';

export const scheduleTranslation = pgTable(
  'schedule_translation',
  {
    scheduleId: integer('schedule_id').notNull(),
    languageCode: varchar('language_code', { length: 255 }).notNull(),
    patientHistory: text('patient_history'),
    detail: text()
  },
  (table) => [
    foreignKey({
      columns: [table.scheduleId],
      foreignColumns: [schedules.scheduleId],
      name: 'schedule_translation_schedule_id_fkey'
    }),
    primaryKey({
      columns: [table.scheduleId, table.languageCode],
      name: 'schedule_translation_pkey'
    })
  ]
);

export const scheduleTranslationRelations = relations(
  scheduleTranslation,
  ({ one }) => ({
    schedule: one(schedules, {
      fields: [scheduleTranslation.scheduleId],
      references: [schedules.scheduleId]
    })
  })
);
