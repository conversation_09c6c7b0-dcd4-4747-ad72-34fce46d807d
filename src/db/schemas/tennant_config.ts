import { relations } from 'drizzle-orm';
import {
  json,
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { tennantMaster } from './tennant_master';

export const tennantConfig = pgTable(
  'tennant_config',
  {
    id: serial().primaryKey().notNull(),
    tennantId: integer('tennant_id'),
    themeColor: json('theme_color'),
    createdAt: timestamp('created_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    updatedAt: timestamp('updated_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    themeConfig: json('theme_config'),
    isExternal: boolean('is_external').default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'tennant_config_tennant_id_fkey'
    })
  ]
);

export const tennantConfigRelations = relations(tennantConfig, ({ one }) => ({
  tennantMaster: one(tennantMaster, {
    fields: [tennantConfig.tennantId],
    references: [tennantMaster.id]
  })
}));
