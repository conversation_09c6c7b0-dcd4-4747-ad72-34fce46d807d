import { relations } from 'drizzle-orm';
import {
  text,
  uuid,
  serial,
  pgTable,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { chatFiles } from './chat_files';
import { chatMessages } from './chat_messages';
import { chatRoomMembers } from './chat_room_members';

export const chatRooms = pgTable('chat_rooms', {
  id: serial().primaryKey().notNull(),
  roomName: text('room_name').notNull(),
  roomIdentifier: uuid('room_identifier').defaultRandom(),
  deleted: boolean().default(false).notNull(),
  deletedAt: timestamp('deleted_at', { mode: 'string' }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
  serviceKey: text('service_key'),
  description: text(),
  lastMessageAt: timestamp('last_message_at', { mode: 'string' }),
  lastMessage: text('last_message')
});

export const chatRoomsRelations = relations(chatRooms, ({ many }) => ({
  chatRoomMembers: many(chatRoomMembers),
  chatMessages: many(chatMessages),
  chatFiles: many(chatFiles)
}));
