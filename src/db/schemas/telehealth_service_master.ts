import { sql, relations } from 'drizzle-orm';
import {
  uuid,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { servicePaymentMapping } from './service_payment_mapping';
import { subscriptionPlans } from './subscription_plans';
import { telehealthServices } from './telehealth_services';
import { tennantMaster } from './tennant_master';
import { userSubscription } from './user_subscription';

export const telehealthServiceMaster = pgTable(
  'telehealth_service_master',
  {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 200 }).notNull(),
    description: varchar({ length: 500 }),
    serviceMasterGuid: uuid('service_master_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    tennantId: integer('tennant_id'),
    createdAt: timestamp('created_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    updatedAt: timestamp('updated_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    serviceKey: varchar('service_key', { length: 500 }),
    initialServiceId: integer('initial_service_id'),
    categories: integer().array().default([]),
    filterDisplay: boolean('filter_display').default(true).notNull(),
    icons: varchar({ length: 500 }).default(sql`NULL`),
    icon: varchar({ length: 500 }).default(sql`NULL`)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'telehealth_service_master_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.initialServiceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_master_initial_service_id_fkey'
    })
  ]
);

export const telehealthServiceMasterRelations = relations(
  telehealthServiceMaster,
  ({ one, many }) => ({
    subscriptionPlans: many(subscriptionPlans),
    userSubscriptions: many(userSubscription),
    servicePaymentMappings: many(servicePaymentMapping),
    telehealthServices: many(telehealthServices, {
      relationName:
        'telehealthServices_serviceMasterId_telehealthServiceMaster_id'
    }),
    tennantMaster: one(tennantMaster, {
      fields: [telehealthServiceMaster.tennantId],
      references: [tennantMaster.id]
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceMaster.initialServiceId],
      references: [telehealthServices.id],
      relationName:
        'telehealthServiceMaster_initialServiceId_telehealthServices_id'
    })
  })
);
