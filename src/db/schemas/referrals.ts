import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { faxes } from './faxes';
import { jobs } from './jobs';
import { orders } from './orders';
import { products } from './products';
import { referralTracking } from './referral_tracking';
import { users } from './users';

export const enumReferralsStatus = pgEnum('enum_referrals_status', [
  'issued',
  'canceled',
  'pdf_pending'
]);

export const referrals = pgTable(
  'referrals',
  {
    referralId: serial('referral_id').primaryKey().notNull(),
    referredBy: integer('referred_by'),
    referredFor: integer('referred_for'),
    doctorId: integer('doctor_id'),
    type: text().notNull(),
    name: text(),
    path: text().notNull(),
    status: enumReferralsStatus(),
    orderId: integer('order_id'),
    detail: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    productId: integer('product_id'),
    prescriptionViewed: boolean('prescription_viewed').default(false),
    prescriptionProcessedBy: varchar('prescription_processed_by', {
      length: 100
    }),
    lifefileOrderId: integer('lifefile_order_id'),
    dosespotPrescriptionId: integer('dosespot_prescription_id')
  },
  (table) => [
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'referrals_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'referrals_order_id_fkey'
    }),
    foreignKey({
      columns: [table.referredBy],
      foreignColumns: [users.userId],
      name: 'referrals_referred_by_fkey'
    }),
    foreignKey({
      columns: [table.referredFor],
      foreignColumns: [users.userId],
      name: 'referrals_referred_for_fkey'
    }),
    foreignKey({
      columns: [table.productId],
      foreignColumns: [products.productId],
      name: 'referrals_product_id_fkey'
    })
  ]
);

export const referralsRelations = relations(referrals, ({ one, many }) => ({
  jobs: many(jobs),
  referralTrackings: many(referralTracking),
  faxes: many(faxes),
  user_doctorId: one(users, {
    fields: [referrals.doctorId],
    references: [users.userId],
    relationName: 'referrals_doctorId_users_userId'
  }),
  order: one(orders, {
    fields: [referrals.orderId],
    references: [orders.id]
  }),
  user_referredBy: one(users, {
    fields: [referrals.referredBy],
    references: [users.userId],
    relationName: 'referrals_referredBy_users_userId'
  }),
  user_referredFor: one(users, {
    fields: [referrals.referredFor],
    references: [users.userId],
    relationName: 'referrals_referredFor_users_userId'
  }),
  product: one(products, {
    fields: [referrals.productId],
    references: [products.productId]
  })
}));
