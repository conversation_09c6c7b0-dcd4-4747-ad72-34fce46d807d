import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { chatRooms } from './chat_rooms';
import { users } from './users';

export const chatRoomMembers = pgTable(
  'chat_room_members',
  {
    id: serial().primaryKey().notNull(),
    userId: serial('user_id').notNull(),
    roomId: serial('room_id').notNull(),
    deleted: boolean().default(false).notNull(),
    deletedAt: timestamp('deleted_at', { mode: 'string' }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'chat_room_members_user_id_users_user_id_fk'
    }),
    foreignKey({
      columns: [table.roomId],
      foreignColumns: [chatRooms.id],
      name: 'chat_room_members_room_id_chat_rooms_id_fk'
    })
  ]
);

export const chatRoomMembersRelations = relations(
  chatRoomMembers,
  ({ one }) => ({
    user: one(users, {
      fields: [chatRoomMembers.userId],
      references: [users.userId]
    }),
    chatRoom: one(chatRooms, {
      fields: [chatRoomMembers.roomId],
      references: [chatRooms.id]
    })
  })
);
