import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { drugDays } from './drug_days';
import { drugsCategory } from './drugs_category';

export const drugs = pgTable(
  'drugs',
  {
    drugId: serial('drug_id').primaryKey().notNull(),
    categoryId: integer('category_id'),
    drugFullName: varchar('drug_full_name', { length: 200 }).notNull(),
    tier: varchar({ length: 200 }).notNull(),
    price: varchar({ length: 200 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    quantity: varchar({ length: 200 })
  },
  (table) => [
    foreignKey({
      columns: [table.categoryId],
      foreignColumns: [drugsCategory.categoryId],
      name: 'drugs_category_id_fkey'
    })
  ]
);

export const drugsRelations = relations(drugs, ({ one, many }) => ({
  drugsCategory: one(drugsCategory, {
    fields: [drugs.categoryId],
    references: [drugsCategory.categoryId]
  }),
  drugDays: many(drugDays)
}));
