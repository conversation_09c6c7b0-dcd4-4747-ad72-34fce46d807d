import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const loginRequests = pgTable(
  'login_requests',
  {
    loginRequestId: serial('login_request_id').primaryKey().notNull(),
    userId: integer('user_id'),
    badRequest: boolean('bad_request').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'login_requests_user_id_fkey'
    })
  ]
);

export const loginRequestsRelations = relations(loginRequests, ({ one }) => ({
  user: one(users, {
    fields: [loginRequests.userId],
    references: [users.userId]
  })
}));
