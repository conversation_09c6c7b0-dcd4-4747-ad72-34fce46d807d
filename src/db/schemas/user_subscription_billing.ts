import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { subscriptionPlans } from './subscription_plans';
import { users } from './users';

export const userSubscriptionBilling = pgTable(
  'user_subscription_billing',
  {
    billingId: serial('billing_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    planId: integer('plan_id').notNull(),
    basePlanPrice: integer('base_plan_price').notNull(),
    discount: integer().notNull(),
    invoiceNumber: text('invoice_number'),
    specialDiscounts: text('special_discounts'),
    addons: text(),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    totalPrice: text('total_price').default('0'),
    billed: boolean().default(false),
    invoicePdfPath: text('invoice_pdf_path'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.planId],
      foreignColumns: [subscriptionPlans.planId],
      name: 'user_subscription_billing_plan_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_subscription_billing_user_id_fkey'
    })
  ]
);

export const userSubscriptionBillingRelations = relations(
  userSubscriptionBilling,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [userSubscriptionBilling.planId],
      references: [subscriptionPlans.planId]
    }),
    user: one(users, {
      fields: [userSubscriptionBilling.userId],
      references: [users.userId]
    })
  })
);
