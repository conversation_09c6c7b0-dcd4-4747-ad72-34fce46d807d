import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { prescriptionTransferMedications } from './prescription_transfer_medications';
import { users } from './users';

export const prescriptionTransferRequest = pgTable(
  'prescription_transfer_request',
  {
    requestId: serial('request_id').primaryKey().notNull(),
    userId: integer('user_id'),
    zipcode: text().notNull(),
    pharmacyName: text('pharmacy_name').notNull(),
    pharmacyAddress: text('pharmacy_address').notNull(),
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    transferAll: boolean('transfer_all').default(false).notNull(),
    phone: text().notNull(),
    dob: text().notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'prescription_transfer_request_user_id_fkey'
    })
  ]
);

export const prescriptionTransferRequestRelations = relations(
  prescriptionTransferRequest,
  ({ one, many }) => ({
    prescriptionTransferMedications: many(prescriptionTransferMedications),
    user: one(users, {
      fields: [prescriptionTransferRequest.userId],
      references: [users.userId]
    })
  })
);
