import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { drugs } from './drugs';

export const drugDays = pgTable(
  'drug_days',
  {
    id: serial().primaryKey().notNull(),
    drugId: integer('drug_id'),
    drugDays: integer('drug_days').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.drugId],
      foreignColumns: [drugs.drugId],
      name: 'drug_days_drug_id_fkey'
    })
  ]
);

export const drugDaysRelations = relations(drugDays, ({ one }) => ({
  drug: one(drugs, {
    fields: [drugDays.drugId],
    references: [drugs.drugId]
  })
}));
