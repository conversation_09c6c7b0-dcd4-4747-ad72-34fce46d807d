import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceMaster } from './telehealth_service_master';
import { userSubscription } from './user_subscription';
import { userSubscriptionBilling } from './user_subscription_billing';
import { users } from './users';

export const enumSubscriptionPlansBillingCycle = pgEnum(
  'enum_subscription_plans_billing_cycle',
  ['monthly', 'yearly', 'weekly']
);
export const subscriptionPlans = pgTable(
  'subscription_plans',
  {
    planId: serial('plan_id').primaryKey().notNull(),
    name: text().notNull(),
    description: text(),
    price: integer().notNull(),
    currency: text().notNull(),
    billingCycle: enumSubscriptionPlansBillingCycle('billing_cycle').notNull(),
    billingInterval: integer('billing_interval').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    paypalPlanId: text('paypal_plan_id'),
    serviceId: integer('service_id'),
    serviceKey: varchar('service_key', { length: 255 }),
    serviceMasterId: integer('service_master_id'),
    recurlyPlanId: varchar('recurly_plan_id', { length: 255 }),
    stripePlanId: varchar('stripe_plan_id', { length: 255 }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deletedBy: integer('deleted_by'),
    visitCount: integer('visit_count').default(1)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'subscription_plans_service_master_id_fkey'
    }),
    foreignKey({
      columns: [table.deletedBy],
      foreignColumns: [users.userId],
      name: 'subscription_plans_deleted_by_fkey'
    })
  ]
);

export const subscriptionPlansRelations = relations(
  subscriptionPlans,
  ({ one, many }) => ({
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [subscriptionPlans.serviceMasterId],
      references: [telehealthServiceMaster.id]
    }),
    user: one(users, {
      fields: [subscriptionPlans.deletedBy],
      references: [users.userId]
    }),
    userSubscriptionBillings: many(userSubscriptionBilling),
    userSubscriptions: many(userSubscription)
  })
);
