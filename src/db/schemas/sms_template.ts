import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServices } from './telehealth_services';

export const smsTemplate = pgTable(
  'sms_template',
  {
    smsTemplateId: serial('sms_template_id').primaryKey().notNull(),
    smsAction: varchar('sms_action', { length: 100 }).notNull(),
    smsTemplateName: varchar('sms_template_name', { length: 500 }).notNull(),
    serviceId: integer('service_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'sms_template_service_id_fkey'
    })
  ]
);

export const smsTemplateRelations = relations(smsTemplate, ({ one }) => ({
  telehealthService: one(telehealthServices, {
    fields: [smsTemplate.serviceId],
    references: [telehealthServices.id]
  })
}));
