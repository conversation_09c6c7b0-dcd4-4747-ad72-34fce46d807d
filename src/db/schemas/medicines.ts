import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { medicineServicePharmacyMapping } from './medicine_service_pharmacy_mapping';

export const medicines = pgTable('medicines', {
  medicineId: serial('medicine_id').primaryKey().notNull(),
  name: varchar({ length: 50 }).notNull(),
  description: text(),
  brand: varchar({ length: 500 }),
  form: varchar({ length: 500 }),
  dosage: varchar({ length: 500 }),
  quantity: varchar({ length: 500 }),
  refillQuantity: varchar('refill_quantity', { length: 500 }),
  directionQuantity: varchar('direction_quantity', { length: 500 }),
  directionOne: varchar('direction_one', { length: 500 }),
  directionTwo: varchar('direction_two', { length: 500 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
});

export const medicinesRelations = relations(medicines, ({ many }) => ({
  medicineServicePharmacyMappings: many(medicineServicePharmacyMapping)
}));
