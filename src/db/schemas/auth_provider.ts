import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const authProvider = pgTable(
  'auth_provider',
  {
    authId: serial('auth_id').notNull(),
    userId: integer('user_id'),
    jsonTokenId: text('json_token_id'),
    refreshToken: text('refresh_token'),
    iat: timestamp({ withTimezone: true, mode: 'string' }),
    isJtiValid: boolean('is_jti_valid').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'auth_provider_user_id_fkey'
    })
  ]
);

export const authProviderRelations = relations(authProvider, ({ one }) => ({
  user: one(users, {
    fields: [authProvider.userId],
    references: [users.userId]
  })
}));
