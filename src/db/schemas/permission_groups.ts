import { relations } from 'drizzle-orm';
import {
  serial,
  unique,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const permissionGroups = pgTable(
  'permission_groups',
  {
    patientId: integer('patient_id').notNull(),
    associatedUserId: integer('associated_user_id').notNull(),
    groupId: serial('group_id').primaryKey().notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.associatedUserId],
      foreignColumns: [users.userId],
      name: 'permission_groups_associated_user_id_fkey'
    }),
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [users.userId],
      name: 'permission_groups_patient_id_fkey'
    }),
    unique('permission_groups_patient_id_associated_user_id_key').on(
      table.patientId,
      table.associatedUserId
    )
  ]
);

export const permissionGroupsRelations = relations(
  permissionGroups,
  ({ one }) => ({
    user_associatedUserId: one(users, {
      fields: [permissionGroups.associatedUserId],
      references: [users.userId],
      relationName: 'permissionGroups_associatedUserId_users_userId'
    }),
    user_patientId: one(users, {
      fields: [permissionGroups.patientId],
      references: [users.userId],
      relationName: 'permissionGroups_patientId_users_userId'
    })
  })
);
