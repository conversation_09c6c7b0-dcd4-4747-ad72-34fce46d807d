import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { subscriptionPlans } from './subscription_plans';
import { telehealthServiceMaster } from './telehealth_service_master';
import { users } from './users';

export const userSubscription = pgTable(
  'user_subscription',
  {
    userSubscriptionId: serial('user_subscription_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    planId: integer('plan_id').notNull(),
    startDate: timestamp('start_date', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    noOfIntervals: integer('no_of_intervals'),
    referralCode: text('referral_code'),
    discount: integer().default(0),
    nextBillingDate: timestamp('next_billing_date', {
      withTimezone: true,
      mode: 'string'
    }),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    subscriptionId: text('subscription_id'),
    status: text(),
    serviceId: integer('service_id'),
    serviceKey: varchar('service_key', { length: 255 }),
    serviceMasterId: integer('service_master_id'),
    consultLimit: integer('consult_limit'),
    consultCount: integer('consult_count')
  },
  (table) => [
    foreignKey({
      columns: [table.planId],
      foreignColumns: [subscriptionPlans.planId],
      name: 'user_subscription_plan_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_subscription_user_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'user_subscription_service_master_id_fkey'
    })
  ]
);

export const userSubscriptionRelations = relations(
  userSubscription,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [userSubscription.planId],
      references: [subscriptionPlans.planId]
    }),
    user: one(users, {
      fields: [userSubscription.userId],
      references: [users.userId]
    }),
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [userSubscription.serviceMasterId],
      references: [telehealthServiceMaster.id]
    })
  })
);
