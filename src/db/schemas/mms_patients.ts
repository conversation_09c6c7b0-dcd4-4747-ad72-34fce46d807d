import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { mmsPatientInvitations } from './mms_patient_invitations';
import { mmsPrescriptions } from './mms_prescriptions';
import { patientInsurances } from './patient_insurances';
import { pharmacies } from './pharmacies';
import { users } from './users';

export const mmsPatients = pgTable(
  'mms_patients',
  {
    patientId: serial('patient_id').primaryKey().notNull(),
    patientNo: varchar('patient_no', { length: 255 }).notNull(),
    pharmacyId: integer('pharmacy_id'),
    userId: integer('user_id'),
    firstName: varchar('first_name', { length: 255 }),
    lastName: varchar('last_name', { length: 255 }),
    gender: varchar({ length: 255 }),
    phone: varchar({ length: 255 }),
    email: varchar({ length: 255 }),
    active: varchar({ length: 255 }),
    address: text(),
    notes: text(),
    paymentPreference: text('payment_preference'),
    messagingSettings: text('messaging_settings'),
    diagnostics: text(),
    allergies: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    insurances: text(),
    source: varchar({ length: 255 }),
    dob: text(),
    isSmoker: boolean('is_smoker').default(false),
    maritalStatus: varchar('marital_status', { length: 20 }),
    weight: numeric({ precision: 10, scale: 3 }),
    isPregnant: boolean('is_pregnant').default(false),
    height: numeric({ precision: 10, scale: 3 }),
    medicalRecordNumber: varchar('medical_record_number', { length: 50 }),
    speciesType: varchar('species_type', { length: 15 }),
    deaRestrictionCode: varchar('dea_restriction_code', { length: 50 }),
    familyEmail: varchar('family_email', { length: 255 }),
    patientRemark: varchar('patient_remark', { length: 255 }),
    patientShortRemark: varchar('patient_short_remark', { length: 255 }),
    familyRemark: varchar('family_remark', { length: 255 }),
    race: varchar({ length: 30 }),
    workPhone: varchar('work_phone', { length: 15 }),
    mobile: varchar({ length: 15 }),
    chartNo: varchar('chart_no', { length: 30 }),
    language: varchar({ length: 30 }),
    ezCap: boolean('ez_cap').default(false),
    discountCode: varchar('discount_code', { length: 30 }),
    shortSode: varchar('short_sode', { length: 30 }),
    priceCodeBrand: varchar('price_code_brand', { length: 30 }),
    priceCodeGeneric: varchar('price_code_generic', { length: 30 }),
    chargeAccount: varchar('charge_account', { length: 30 }),
    printDrugCounselling: varchar('print_drug_counselling', { length: 30 }),
    category: varchar({ length: 30 }),
    preferredDeliveryMethod: varchar('preferred_delivery_method', {
      length: 50
    }),
    driverLicenseNumber: varchar('driver_license_number', { length: 50 }),
    hippaSignature: varchar('hippa_signature', { length: 50 }),
    driverLicenseExpiry: varchar('driver_license_expiry', { length: 50 })
  },
  (table) => [
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'mms_patients_pharmacy_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'mms_patients_user_id_fkey'
    })
  ]
);

export const mmsPatientsRelations = relations(mmsPatients, ({ one, many }) => ({
  pharmacy: one(pharmacies, {
    fields: [mmsPatients.pharmacyId],
    references: [pharmacies.pharmacyId]
  }),
  user: one(users, {
    fields: [mmsPatients.userId],
    references: [users.userId]
  }),
  mmsPrescriptions: many(mmsPrescriptions),
  patientInsurances: many(patientInsurances),
  mmsPatientInvitations: many(mmsPatientInvitations)
}));
