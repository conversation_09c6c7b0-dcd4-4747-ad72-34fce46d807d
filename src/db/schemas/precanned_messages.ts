import { text, serial, pgTable, varchar, timestamp } from 'drizzle-orm/pg-core';

export const precannedMessages = pgTable('precanned_messages', {
  messageId: serial('message_id').primaryKey().notNull(),
  message: text().notNull(),
  type: varchar({ length: 255 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
