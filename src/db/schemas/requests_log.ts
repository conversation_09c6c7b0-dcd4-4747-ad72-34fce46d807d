import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const requestsLog = pgTable(
  'requests_log',
  {
    requestLogId: serial('request_log_id').primaryKey().notNull(),
    userId: integer('user_id'),
    installType: varchar('install_type', { length: 255 }),
    method: varchar({ length: 255 }).notNull(),
    responseHttpStatus: integer('response_http_status').notNull(),
    endpoint: text().notNull(),
    timestamp: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'requests_log_user_id_fkey'
    })
  ]
);

export const requestsLogRelations = relations(requestsLog, ({ one }) => ({
  user: one(users, {
    fields: [requestsLog.userId],
    references: [users.userId]
  })
}));
