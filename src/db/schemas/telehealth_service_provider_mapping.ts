import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServices } from './telehealth_services';
import { users } from './users';

export const telehealthServiceProviderMapping = pgTable(
  'telehealth_service_provider_mapping',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    providerId: integer('provider_id'),
    costPrice: numeric('cost_price').default('0'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    status: boolean().default(true)
  },
  (table) => [
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [users.userId],
      name: 'telehealth_service_provider_mapping_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_provider_mapping_service_id_fkey'
    })
  ]
);

export const telehealthServiceProviderMappingRelations = relations(
  telehealthServiceProviderMapping,
  ({ one }) => ({
    user: one(users, {
      fields: [telehealthServiceProviderMapping.providerId],
      references: [users.userId]
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceProviderMapping.serviceId],
      references: [telehealthServices.id]
    })
  })
);
