import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { mmsPatients } from './mms_patients';
import { mmsPrescriptionRefills } from './mms_prescription_refills';
export const mmsPrescriptions = pgTable(
  'mms_prescriptions',
  {
    prescriptionId: serial('prescription_id').primaryKey().notNull(),
    patientId: integer('patient_id').notNull(),
    rxNo: varchar('rx_no', { length: 255 }),
    authRefills: varchar('auth_refills', { length: 255 }),
    drugInfo: text('drug_info'),
    qtyOrdered: numeric('qty_ordered'),
    dateOrdered: varchar('date_ordered', { length: 255 }),
    dateExpires: varchar('date_expires', { length: 255 }),
    discontinued: varchar({ length: 255 }),
    sig: varchar({ length: 255 }),
    prescriber: text(),
    diagnostics: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    auth: varchar({ length: 50 }),
    sourcePms: varchar('source_pms', { length: 50 }),
    refillNo: varchar('refill_no', { length: 50 }),
    pharmacyId: integer('pharmacy_id'),
    dateFilled: varchar('date_filled', { length: 50 }),
    discontinueReason: varchar('discontinue_reason', { length: 50 }),
    dateDiscontinued: varchar('date_discontinued', { length: 50 }),
    status: varchar({ length: 50 }),
    daysSupplied: varchar('days_supplied', { length: 50 }),
    rxSerialNo: varchar('rx_serial_no', { length: 50 }),
    billType: varchar('bill_type', { length: 50 }),
    billAs: varchar('bill_as', { length: 50 }),
    qtyDispensed: varchar('qty_dispensed', { length: 50 }),
    deliveryMethod: varchar('delivery_method', { length: 50 }),
    holdRx: varchar('hold_rx', { length: 50 }),
    rph: varchar({ length: 50 }),
    datePicked: varchar('date_picked', { length: 30 }),
    awp: varchar({ length: 50 }),
    timePicked: varchar('time_picked', { length: 30 }),
    pickedUp: varchar('picked_up', { length: 50 }),
    prescriptionNotes: varchar('prescription_notes', { length: 255 }),
    deliveryDate: varchar('delivery_date', { length: 30 }),
    trackingUrl: varchar('tracking_url', { length: 50 }),
    shippingService: varchar('shipping_service', { length: 50 }),
    sourceScriptId: varchar('source_script_id', { length: 50 }),
    flag340B: varchar({ length: 50 }),
    dawCode: varchar('daw_code', { length: 50 }),
    dispenseAsWritten: varchar('dispense_as_written', { length: 50 }),
    sourceScriptMessage: varchar('source_script_message', { length: 50 }),
    costPrice: varchar('cost_price', { length: 50 }),
    rxAmount: varchar('rx_amount', { length: 50 }),
    dispFee: varchar('disp_fee', { length: 50 }),
    dosageFields: text('dosage_fields'),
    patientCopay: varchar('patient_copay', { length: 50 }),
    billed: varchar({ length: 50 }),
    primaryInsurance: text('primary_insurance'),
    fillListIndicator: varchar('fill_list_indicator', { length: 50 }),
    submissionClarCode: varchar('submission_clar_code', { length: 50 }),
    horizonGraveyardCode: varchar('horizon_graveyard_code', { length: 50 }),
    roomNumber: varchar('room_number', { length: 10 }),
    nursingHomeId: varchar('nursing_home_id', { length: 30 }),
    locationCode: varchar('location_code', { length: 20 }),
    facilityCode: varchar('facility_code', { length: 20 }),
    nursingHome: varchar('nursing_home', { length: 50 }),
    wingCode1: varchar('wing_code1', { length: 50 }),
    referingDoctor: varchar('refering_doctor', { length: 50 }),
    xferToPharmacyName: varchar('xfer_to_pharmacy_name', { length: 50 }),
    wingCode2: varchar('wing_code2', { length: 50 }),
    xferToPharmacyAddress1: varchar('xfer_to_pharmacy_address1', {
      length: 255
    }),
    xferToPharmacyAddress2: varchar('xfer_to_pharmacy_address2', {
      length: 255
    }),
    xferToPharmacyCity: varchar('xfer_to_pharmacy_city', { length: 50 }),
    xferToPharmacyPhone: varchar('xfer_to_pharmacy_phone', { length: 20 }),
    xferToPharmacyNpi: varchar('xfer_to_pharmacy_npi', { length: 30 }),
    xferToPharmacyNcpdp: varchar('xfer_to_pharmacy_ncpdp', { length: 30 }),
    billStatus: varchar('bill_status', { length: 50 }),
    xferToPharmacyDea: varchar('xfer_to_pharmacy_dea', { length: 30 }),
    billStatusText: varchar('bill_status_text', { length: 50 }),
    workflowStatus: varchar('workflow_status', { length: 50 }),
    prescribedDrug: varchar('prescribed_drug', { length: 50 }),
    workflowStatusText: varchar('workflow_status_text', { length: 50 }),
    claimAuthorizationNumber: varchar('claim_authorization_number', {
      length: 50
    }),
    priorAuthNumber: varchar('prior_auth_number', { length: 50 }),
    electionPrescriptionOriginTime: varchar(
      'election_prescription_origin_time',
      { length: 50 }
    )
  },
  (table) => [
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [mmsPatients.patientId],
      name: 'mms_prescriptions_patient_id_fkey'
    })
  ]
);

export const mmsPrescriptionsRelations = relations(
  mmsPrescriptions,
  ({ one, many }) => ({
    mmsPatient: one(mmsPatients, {
      fields: [mmsPrescriptions.patientId],
      references: [mmsPatients.patientId]
    }),
    mmsPrescriptionRefills: many(mmsPrescriptionRefills)
  })
);
