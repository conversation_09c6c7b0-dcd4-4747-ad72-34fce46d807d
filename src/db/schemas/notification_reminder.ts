import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { users } from './users';

export const enumNotificationReminderNotificationForType = pgEnum(
  'enum_notification_reminder_notification_for_type',
  ['schedule_pending', 'intake_questionnaire', 'follow_up']
);
export const enumNotificationReminderNotificationStatus = pgEnum(
  'enum_notification_reminder_notification_status',
  ['pending', 'sent', 'failed']
);

export const notificationReminder = pgTable(
  'notification_reminder',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id'),
    userId: integer('user_id'),
    lastReminderDate: timestamp('last_reminder_date', {
      withTimezone: true,
      mode: 'string'
    }),
    reminderCount: integer('reminder_count').default(0).notNull(),
    notificationForType: enumNotificationReminderNotificationForType(
      'notification_for_type'
    )
      .default('intake_questionnaire')
      .notNull(),
    notificationStatus: enumNotificationReminderNotificationStatus(
      'notification_status'
    )
      .default('pending')
      .notNull(),
    errorMessage: text('error_message'),
    orderGuid: varchar('order_guid', { length: 50 }),
    userGuid: varchar('user_guid', { length: 50 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'notification_reminder_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'notification_reminder_user_id_fkey'
    })
  ]
);

export const notificationReminderRelations = relations(
  notificationReminder,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [notificationReminder.orderId],
      references: [telehealthServiceOrder.id]
    }),
    user: one(users, {
      fields: [notificationReminder.userId],
      references: [users.userId]
    })
  })
);
