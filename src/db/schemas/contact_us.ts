import {
  serial,
  pgTable,
  varchar,
  integer,
  timestamp
} from 'drizzle-orm/pg-core';

export const contactUs = pgTable('contact_us', {
  contactUsId: serial('contact_us_id').primaryKey().notNull(),
  userId: integer('user_id').notNull(),
  dateRequested: timestamp('date_requested', {
    withTimezone: true,
    mode: 'string'
  }),
  title: varchar({ length: 1000 }).default('').notNull(),
  question: varchar({ length: 1000 }).default('').notNull(),
  queryText: varchar({ length: 4000 }).default('').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
