import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { states } from './states';
import { users } from './users';

export const userLicenses = pgTable(
  'user_licenses',
  {
    userLicenseId: serial('user_license_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    number: varchar({ length: 255 }).notNull(),
    stateId: integer('state_id').notNull(),
    state: varchar({ length: 255 }).notNull(),
    endDate: timestamp('end_date', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_licenses_user_id_fkey'
    }),
    foreignKey({
      columns: [table.stateId],
      foreignColumns: [states.stateId],
      name: 'user_licenses_state_id_fkey'
    })
  ]
);

export const userLicensesRelations = relations(userLicenses, ({ one }) => ({
  user: one(users, {
    fields: [userLicenses.userId],
    references: [users.userId]
  }),
  state: one(states, {
    fields: [userLicenses.stateId],
    references: [states.stateId]
  })
}));
