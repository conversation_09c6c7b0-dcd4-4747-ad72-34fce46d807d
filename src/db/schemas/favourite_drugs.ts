import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const favouriteDrugs = pgTable(
  'favourite_drugs',
  {
    drugId: serial('drug_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    drugName: varchar('drug_name', { length: 255 }).notNull(),
    brand: varchar({ length: 255 }).notNull(),
    form: varchar({ length: 255 }).notNull(),
    dosage: varchar({ length: 255 }).notNull(),
    quantity: varchar({ length: 255 }).notNull(),
    refillQuantity: varchar('refill_quantity', { length: 255 }).notNull(),
    favourite: boolean().default(true).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    directionQuantity: varchar('direction_quantity', { length: 255 }),
    directionOne: varchar('direction_one', { length: 255 }),
    directionTwo: varchar('direction_two', { length: 255 }),
    tennantId: varchar('tennant_id', { length: 255 }),
    pharmacyName: varchar('pharmacy_name', { length: 255 }),
    quantityUnit: varchar('quantity_unit', { length: 100 }),
    erxProductId: varchar('erx_product_id', { length: 200 }),
    displayOrder: integer('display_order'),
    comments: text()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'favourite_drugs_user_id_fkey'
    })
  ]
);

export const favouriteDrugsRelations = relations(favouriteDrugs, ({ one }) => ({
  user: one(users, {
    fields: [favouriteDrugs.userId],
    references: [users.userId]
  })
}));
