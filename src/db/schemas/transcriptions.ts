import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { users } from './users';

export const transcriptions = pgTable(
  'transcriptions',
  {
    transcriptionId: serial('transcription_id').primaryKey().notNull(),
    orderId: text('order_id'),
    tuserId: integer('tuser_id'),
    status: text(),
    transcript: text(),
    updatedTranscript: text('updated_transcript'),
    audioStream: text('audio_stream'),
    archiveId: varchar('archive_id', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.orderId],
      name: 'transcriptions_order_id_fkey'
    }),
    foreignKey({
      columns: [table.tuserId],
      foreignColumns: [users.userId],
      name: 'transcriptions_tuser_id_fkey'
    })
  ]
);

export const transcriptionsRelations = relations(transcriptions, ({ one }) => ({
  order: one(orders, {
    fields: [transcriptions.orderId],
    references: [orders.orderId]
  }),
  user: one(users, {
    fields: [transcriptions.tuserId],
    references: [users.userId]
  })
}));
