import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { users } from './users';

export const telehealthServiceQuestionAnswerDump = pgTable(
  'telehealth_service_question_answer_dump',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id'),
    questionText: varchar('question_text', { length: 2000 }).notNull(),
    answer: varchar({ length: 2000 }).default('false'),
    otherText: varchar('other_text', { length: 2000 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    selectionOption: text('selection_option'),
    answeredBy: integer('answered_by')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'telehealth_service_question_answer_dump_service_order_id_fkey'
    }),
    foreignKey({
      columns: [table.answeredBy],
      foreignColumns: [users.userId],
      name: 'telehealth_service_question_answer_dump_answered_by_fkey'
    })
  ]
);

export const telehealthServiceQuestionAnswerDumpRelations = relations(
  telehealthServiceQuestionAnswerDump,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [telehealthServiceQuestionAnswerDump.serviceOrderId],
      references: [telehealthServiceOrder.id]
    }),
    user: one(users, {
      fields: [telehealthServiceQuestionAnswerDump.answeredBy],
      references: [users.userId]
    })
  })
);
