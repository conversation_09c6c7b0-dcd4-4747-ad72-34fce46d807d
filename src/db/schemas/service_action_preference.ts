import { relations } from 'drizzle-orm';
import {
  text,
  json,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServices } from './telehealth_services';
import { tennantMaster } from './tennant_master';

export const serviceActionPreference = pgTable(
  'service_action_preference',
  {
    serviceActionPreferenceId: serial('service_action_preference_id')
      .primaryKey()
      .notNull(),
    serviceId: integer('service_id'),
    tenantId: integer('tenant_id').notNull(),
    actionType: varchar('action_type', { length: 255 }).notNull(),
    webhookUrl: varchar('webhook_url', { length: 255 }),
    smsTemplate: text('sms_template'),
    emailTemplate: varchar('email_template', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    externalApiIntegration: varchar('external_api_integration', {
      length: 255
    }),
    apiKey: text('api_key'),
    apiSecret: text('api_secret'),
    emailSubject: varchar('email_subject', { length: 255 }),
    ccRecipient: json('cc_recipient').default([]),
    bccRecipient: json('bcc_recipient').default([])
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'service_action_preference_service_id_fkey'
    }),
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'service_action_preference_tenant_id_fkey'
    })
  ]
);

export const serviceActionPreferenceRelations = relations(
  serviceActionPreference,
  ({ one }) => ({
    telehealthService: one(telehealthServices, {
      fields: [serviceActionPreference.serviceId],
      references: [telehealthServices.id]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [serviceActionPreference.tenantId],
      references: [tennantMaster.id]
    })
  })
);
