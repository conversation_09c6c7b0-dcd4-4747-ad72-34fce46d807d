import { relations } from 'drizzle-orm';
import {
  pgTable,
  integer,
  timestamp,
  foreignKey,
  primaryKey
} from 'drizzle-orm/pg-core';

import { practiceGroups } from './practice_groups';
import { users } from './users';

export const userPracticeGroups = pgTable(
  'user_practice_groups',
  {
    userId: integer('user_id').notNull(),
    practiceGroupId: integer('practice_group_id').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.practiceGroupId],
      foreignColumns: [practiceGroups.practiceGroupId],
      name: 'user_practice_groups_practice_group_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_practice_groups_user_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.practiceGroupId],
      name: 'user_practice_groups_pkey'
    })
  ]
);

export const userPracticeGroupsRelations = relations(
  userPracticeGroups,
  ({ one }) => ({
    practiceGroup: one(practiceGroups, {
      fields: [userPracticeGroups.practiceGroupId],
      references: [practiceGroups.practiceGroupId]
    }),
    user: one(users, {
      fields: [userPracticeGroups.userId],
      references: [users.userId]
    })
  })
);
