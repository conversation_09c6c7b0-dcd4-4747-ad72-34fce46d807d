import { sql, relations } from 'drizzle-orm';
import {
  text,
  serial,
  unique,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const enumOnehealthLabOrdersPaymentStatus = pgEnum(
  'enum_onehealth_lab_orders_payment_status',
  ['PENDING', 'FAILED', 'PAID']
);
export const enumOnehealthLabOrdersStatus = pgEnum(
  'enum_onehealth_lab_orders_status',
  ['PENDING', 'PAYMENT_PENDING', 'COMPLETE_COLLECTION', 'COMPLETED']
);

export const onehealthLabOrders = pgTable(
  'onehealth_lab_orders',
  {
    id: serial().primaryKey().notNull(),
    orderGuid: varchar('order_guid', { length: 50 }),
    userId: integer('user_id'),
    registeredKitId: varchar('registered_kit_id', { length: 50 }),
    testingKitType: varchar('testing_kit_type', { length: 100 }).default(''),
    totalQuantity: integer('total_quantity').default(1).notNull(),
    usePrescriptionService: boolean('use_prescription_service').default(false),
    interval: varchar({ length: 30 }),
    resultsNeedsRevision: boolean('results_needs_revision').default(false),
    lab: varchar({ length: 30 }).default('rucdr'),
    additionalData: varchar('additional_data', { length: 30 }).default('rucdr'),
    postedData: text('posted_data'),
    company: varchar({ length: 30 }).default('Ravkoo'),
    returnMailer: varchar('return_mailer', { length: 255 }).default(''),
    insuranceEnabled: varchar('insurance_enabled', { length: 255 }).default(''),
    kitIds: varchar('kit_ids', { length: 255 }).default(''),
    patientId: varchar('patient_id', { length: 50 }).default(''),
    hcpId: varchar('hcp_id', { length: 50 }).default(''),
    testCode: varchar('test_code', { length: 50 }).default(''),
    askOnEntry: varchar({ length: 50 }).default(''),
    diagnosticCode: varchar('diagnostic_code', { length: 255 }).default(''),
    diagnoseObservationDate: timestamp('diagnose_observation_date', {
      withTimezone: true,
      mode: 'string'
    }),
    labRefId: varchar('lab_ref_id', { length: 255 }).default(''),
    status: enumOnehealthLabOrdersStatus().default('PENDING'),
    paymentStatus:
      enumOnehealthLabOrdersPaymentStatus('payment_status').default('PENDING'),
    oneHealth: text('one_health'),
    collectionAt: timestamp('collection_at', {
      withTimezone: true,
      mode: 'string'
    }),
    kitRegisterAt: timestamp('kit_register_at', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    serviceCharges: numeric('service_charges').default('0'),
    shippingAmount: numeric('shipping_amount').default('0'),
    totalCost: numeric('total_cost').default('0'),
    labId: varchar('lab_id', { length: 30 }).default(sql`NULL`),
    paymentTransaction: text('payment_transaction'),
    flatPrice: numeric('flat_price').default('0'),
    name: varchar({ length: 150 }).default(''),
    productCode: varchar('product_code', { length: 100 }).default(''),
    discount: numeric().default('0')
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'onehealth_lab_orders_user_id_fkey'
    }),
    unique('onehealth_lab_orders_order_guid_key').on(table.orderGuid)
  ]
);

export const onehealthLabOrdersRelations = relations(
  onehealthLabOrders,
  ({ one }) => ({
    user: one(users, {
      fields: [onehealthLabOrders.userId],
      references: [users.userId]
    })
  })
);
