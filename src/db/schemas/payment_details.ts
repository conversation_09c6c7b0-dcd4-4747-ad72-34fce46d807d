import { sql, relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { servicePaymentMapping } from './service_payment_mapping';
import { transactions } from './transactions';

export const enumPaymentDetailsPaymentGateway = pgEnum(
  'enum_payment_details_payment_gateway',
  ['paypal', 'stripe', 'recurly', 'authorize_net', 'no_payment', 'nmi']
);

export const paymentDetails = pgTable('payment_details', {
  paymentDetailsId: serial('payment_details_id').primaryKey().notNull(),
  paymentGateway: enumPaymentDetailsPaymentGateway('payment_gateway').notNull(),
  paymentGatewayCode: varchar('payment_gateway_code', { length: 255 }),
  paymentGatewayDetails: text('payment_gateway_details'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  tennantId: integer('tennant_id'),
  siteId: varchar('site_id', { length: 255 }).default(sql`NULL`)
});

export const paymentDetailsRelations = relations(
  paymentDetails,
  ({ many }) => ({
    transactions: many(transactions),
    servicePaymentMappings: many(servicePaymentMapping)
  })
);
