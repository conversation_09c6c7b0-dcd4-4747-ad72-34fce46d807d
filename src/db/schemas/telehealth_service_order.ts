import { relations } from 'drizzle-orm';
import {
  serial,
  unique,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { consultNotes } from './consult_notes';
import { consultOrderFiles } from './consult_order_files';
import { consultReassignHistory } from './consult_reassign_history';
import { consultUpdateDetailsHistory } from './consult_update_details_history';
import { followUpReminder } from './follow_up_reminder';
import { insuranceEligibilityLogs } from './insurance_eligibility_logs';
import { jobs } from './jobs';
import { notificationReminder } from './notification_reminder';
import { orders } from './orders';
import { pharmacies } from './pharmacies';
import { referralTracking } from './referral_tracking';
import { refillRequest } from './refill_request';
import { requests } from './requests';
import { schedules } from './schedules';
import { supportNotes } from './support_notes';
import { telehealthServiceQuestionAnswer } from './telehealth_service_question_answer';
import { telehealthServiceQuestionAnswerDump } from './telehealth_service_question_answer_dump';
import { telehealthServices } from './telehealth_services';
import { users } from './users';

export const enumTelehealthServiceOrderClaimType = pgEnum(
  'enum_telehealth_service_order_claim_type',
  ['AUTO', 'WORK']
);
export const enumTelehealthServiceOrderServiceType = pgEnum(
  'enum_telehealth_service_order_service_type',
  ['SYNC', 'ASYNC']
);
export const enumTelehealthServiceOrderStatus = pgEnum(
  'enum_telehealth_service_order_status',
  [
    'pending',
    'accept',
    'completed',
    'errored',
    'cancelled',
    'patient_verification_pending',
    'archive',
    'cancelled_by_provider',
    'LabRequested',
    'LabReceived',
    'schedule_pending',
    'lab_approval_pending',
    'lab_results_approved',
    'lab_results_denied',
    'clinical_denial',
    'cancelled_by_patient',
    'now_show',
    'no_show',
    'payment_pending',
    'pharmacy_pending',
    'questionnaire_pending',
    'user_consent_pending'
  ]
);
export const enumTelehealthServiceOrderVisitType = pgEnum(
  'enum_telehealth_service_order_visit_type',
  ['IN_PERSON', 'ONLINE']
);

export const telehealthServiceOrder: any = pgTable(
  'telehealth_service_order',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    answerGivenBy: integer('answer_given_by'),
    providerId: integer('provider_id'),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    orderGuid: varchar('order_guid', { length: 50 }),
    status: enumTelehealthServiceOrderStatus(),
    prescriptionDelivery: boolean('prescription_delivery').default(false),
    ravkooPrescriptionOption: boolean('ravkoo_prescription_option').default(
      false
    ),
    pharmacyName: varchar('pharmacy_name', { length: 100 }).default(''),
    pharmacyPhone: varchar('pharmacy_phone', { length: 20 }).default(''),
    pharmacyAddress: varchar('pharmacy_address', { length: 255 }).default(''),
    serviceType:
      enumTelehealthServiceOrderServiceType('service_type').default('SYNC'),
    releaseMedical: boolean('release_medical').default(false),
    releaseMedicalAt: timestamp('release_medical_at', {
      withTimezone: true,
      mode: 'string'
    }),
    isCancelledByProvider: boolean('is_cancelled_by_provider').default(false),
    cancelledAt: timestamp('cancelled_at', {
      withTimezone: true,
      mode: 'string'
    }),
    pharmacyCity: varchar('pharmacy_city', { length: 20 }),
    pharmacyState: varchar('pharmacy_state', { length: 20 }),
    pharmacyZip: varchar('pharmacy_zip', { length: 20 }),
    pharmacyFax: varchar('pharmacy_fax', { length: 20 }),
    pharmacyPreference: integer('pharmacy_preference').default(0).notNull(),
    claimType: enumTelehealthServiceOrderClaimType('claim_type'),
    claimId: varchar('claim_id', { length: 255 }),
    payorName: varchar('payor_name', { length: 255 }),
    injuryDate: timestamp('injury_date', {
      withTimezone: true,
      mode: 'string'
    }),
    currentMedicines: varchar('current_medicines', { length: 255 }),
    allergiesToMedicines: varchar('allergies_to_medicines', { length: 255 }),
    otherAllergies: varchar('other_allergies', { length: 255 }),
    doctorNotes: varchar('doctor_notes', { length: 255 }),
    abnormalFindings: varchar('abnormal_findings', { length: 255 }),
    termsAndConditionsAccepted: boolean(
      'terms_and_conditions_accepted'
    ).default(false),
    cancellationReason: varchar('cancellation_reason', { length: 255 }),
    isRefillRequest: boolean('is_refill_request').default(false),
    externalOrderId: varchar('external_order_id', { length: 50 }),
    followUp: integer('follow_up'),
    visitType: enumTelehealthServiceOrderVisitType('visit_type'),
    completionReason: varchar('completion_reason', { length: 255 }),
    sessionType: varchar('session_type', { length: 255 }),
    scheduleType: varchar('schedule_type', { length: 255 }),
    affiliateid: varchar({ length: 255 }),
    clientName: varchar('client_name', { length: 255 }),
    dosespotPharmacyId: varchar('dosespot_pharmacy_id', { length: 200 }),
    pharmacyNcpdpId: varchar('pharmacy_ncpdp_id', { length: 255 }),
    erxPrescriptionVisitiedAt: timestamp('erx_prescription_visitied_at', {
      withTimezone: true,
      mode: 'string'
    }),
    completedAt: timestamp('completed_at', {
      withTimezone: true,
      mode: 'string'
    }),
    pharmacyId: integer('pharmacy_id')
  },
  (table) => [
    foreignKey({
      columns: [table.answerGivenBy],
      foreignColumns: [users.userId],
      name: 'telehealth_service_order_answer_given_by_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'telehealth_service_order_order_id_fkey'
    }),
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [users.userId],
      name: 'telehealth_service_order_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_order_service_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'telehealth_service_order_pharmacy_id_fkey'
    }),
    unique('telehealth_service_order_order_guid_key').on(table.orderGuid)
  ]
);

export const telehealthServiceOrderRelations = relations(
  telehealthServiceOrder,
  ({ one, many }) => ({
    consultNotes: many(consultNotes),
    requests: many(requests),
    user_answerGivenBy: one(users, {
      fields: [telehealthServiceOrder.answerGivenBy],
      references: [users.userId],
      relationName: 'telehealthServiceOrder_answerGivenBy_users_userId'
    }),
    order: one(orders, {
      fields: [telehealthServiceOrder.orderId],
      references: [orders.id],
      relationName: 'telehealthServiceOrder_orderId_orders_id'
    }),
    user_providerId: one(users, {
      fields: [telehealthServiceOrder.providerId],
      references: [users.userId],
      relationName: 'telehealthServiceOrder_providerId_users_userId'
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceOrder.serviceId],
      references: [telehealthServices.id]
    }),
    pharmacy: one(pharmacies, {
      fields: [telehealthServiceOrder.pharmacyId],
      references: [pharmacies.pharmacyId]
    }),
    orders: many(orders, {
      relationName: 'orders_orderGuid_telehealthServiceOrder_orderGuid'
    }),
    consultUpdateDetailsHistories_serviceOrderId: many(
      consultUpdateDetailsHistory,
      {
        relationName:
          'consultUpdateDetailsHistory_serviceOrderId_telehealthServiceOrder_id'
      }
    ),

    followUpReminders_orderId: many(followUpReminder, {
      relationName: 'followUpReminder_orderId_telehealthServiceOrder_id'
    }),
    followUpReminders_nextOrderId: many(followUpReminder, {
      relationName: 'followUpReminder_nextOrderId_telehealthServiceOrder_id'
    }),
    consultOrderFiles: many(consultOrderFiles),
    jobs: many(jobs),
    telehealthServiceQuestionAnswerDumps: many(
      telehealthServiceQuestionAnswerDump
    ),
    consultReassignHistories: many(consultReassignHistory),
    referralTrackings: many(referralTracking),
    supportNotes: many(supportNotes),
    telehealthServiceQuestionAnswers: many(telehealthServiceQuestionAnswer),
    schedules_orderGuid: many(schedules, {
      relationName: 'schedules_orderGuid_telehealthServiceOrder_orderGuid'
    }),
    schedules_orderId: many(schedules, {
      relationName: 'schedules_orderId_telehealthServiceOrder_id'
    }),
    refillRequests: many(refillRequest),
    insuranceEligibilityLogs: many(insuranceEligibilityLogs),
    notificationReminders: many(notificationReminder)
  })
);
