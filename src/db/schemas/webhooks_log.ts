import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

export const enumWebhooksLogActionType = pgEnum(
  'enum_webhooks_log_action_type',
  ['webhook', 'prescription_api']
);
export const enumWebhooksLogStatus = pgEnum('enum_webhooks_log_status', [
  'pending',
  'success',
  'failed'
]);

export const webhooksLog = pgTable('webhooks_log', {
  webhooksLogId: serial('webhooks_log_id').primaryKey().notNull(),
  body: varchar({ length: 5000 }),
  url: varchar({ length: 255 }).notNull(),
  responseStatus: varchar('response_status', { length: 255 }),
  caseId: varchar('case_id', { length: 255 }).notNull(),
  status: enumWebhooksLogStatus(),
  failedMessage: text('failed_message'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  type: varchar({ length: 255 }),
  headers: text(),
  actionType: enumWebhooksLogActionType('action_type')
});
