import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const enumStripeUserPaymentDetailsPaymentStatus = pgEnum(
  'enum_stripe_user_payment_details_payment_status',
  ['success', 'failed', 'action_required']
);

export const stripeUserPaymentDetails = pgTable(
  'stripe_user_payment_details',
  {
    id: serial().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    paymentMethodId: text('payment_method_id'),
    offSessionPaymentAllowed: boolean('off_session_payment_allowed'),
    paymentStatus: enumStripeUserPaymentDetailsPaymentStatus('payment_status')
      .default('success')
      .notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'stripe_user_payment_details_user_id_fkey'
    })
  ]
);

export const stripeUserPaymentDetailsRelations = relations(
  stripeUserPaymentDetails,
  ({ one }) => ({
    user: one(users, {
      fields: [stripeUserPaymentDetails.userId],
      references: [users.userId]
    })
  })
);
