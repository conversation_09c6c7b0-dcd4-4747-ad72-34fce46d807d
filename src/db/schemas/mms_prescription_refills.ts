import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { mmsPrescriptions } from './mms_prescriptions';

export const mmsPrescriptionRefills = pgTable(
  'mms_prescription_refills',
  {
    refillId: serial('refill_id').primaryKey().notNull(),
    prescriptionId: integer('prescription_id').notNull(),
    refillNo: varchar('refill_no', { length: 255 }),
    dateFilled: varchar('date_filled', { length: 255 }),
    datePicked: varchar('date_picked', { length: 255 }),
    qtyOrdered: numeric('qty_ordered'),
    qtyDispensed: numeric('qty_dispensed'),
    notes: text(),
    deliveryMethod: text('delivery_method'),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    costPrice: numeric('cost_price'),
    dispFee: numeric('disp_fee'),
    rxAmount: numeric('rx_amount'),
    patientCopay: numeric('patient_copay'),
    billedAmount: numeric('billed_amount'),
    primaryInsuranceAmount: numeric('primary_insurance_amount'),
    primaryInsuranceCode: varchar('primary_insurance_code', { length: 255 }),
    secondaryInsuranceAmount: numeric('secondary_insurance_amount'),
    tertiaryInsuranceAmount: numeric('tertiary_insurance_amount'),
    status: varchar({ length: 255 })
  },
  (table) => [
    foreignKey({
      columns: [table.prescriptionId],
      foreignColumns: [mmsPrescriptions.prescriptionId],
      name: 'mms_prescription_refills_prescription_id_fkey'
    })
  ]
);

export const mmsPrescriptionRefillsRelations = relations(
  mmsPrescriptionRefills,
  ({ one }) => ({
    mmsPrescription: one(mmsPrescriptions, {
      fields: [mmsPrescriptionRefills.prescriptionId],
      references: [mmsPrescriptions.prescriptionId]
    })
  })
);
