import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { userEducationalVideos } from './user_educational_videos';
import { users } from './users';

export const educationalVideos = pgTable(
  'educational_videos',
  {
    videoId: serial('video_id').primaryKey().notNull(),
    sessionId: text('session_id'),
    archiveId: text('archive_id'),
    title: text().notNull(),
    description: text(),
    url: text(),
    createdBy: integer('created_by'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    audioStreamScreenshot: text('audio_stream_screenshot')
  },
  (table) => [
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'educational_videos_created_by_fkey'
    })
  ]
);

export const educationalVideosRelations = relations(
  educationalVideos,
  ({ one, many }) => ({
    user: one(users, {
      fields: [educationalVideos.createdBy],
      references: [users.userId]
    }),
    userEducationalVideos: many(userEducationalVideos)
  })
);
