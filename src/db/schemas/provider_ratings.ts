import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { users } from './users';

export const providerRatings = pgTable(
  'provider_ratings',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id'),
    providerId: integer('provider_id'),
    userId: integer('user_id'),
    rating: integer().default(1),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'provider_ratings_order_id_fkey'
    }),
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [users.userId],
      name: 'provider_ratings_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'provider_ratings_user_id_fkey'
    })
  ]
);

export const providerRatingsRelations = relations(
  providerRatings,
  ({ one }) => ({
    order: one(orders, {
      fields: [providerRatings.orderId],
      references: [orders.id]
    }),
    user_providerId: one(users, {
      fields: [providerRatings.providerId],
      references: [users.userId],
      relationName: 'providerRatings_providerId_users_userId'
    }),
    user_userId: one(users, {
      fields: [providerRatings.userId],
      references: [users.userId],
      relationName: 'providerRatings_userId_users_userId'
    })
  })
);
