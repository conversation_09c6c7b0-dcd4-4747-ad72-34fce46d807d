import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { users } from './users';

export const enumVisitSummaryUploadStatusUploadStatus = pgEnum(
  'enum_visit_summary_upload_status_upload_status',
  ['SUCCESS', 'FAILED']
);
export const enumVisitSummaryUploadStatusUploadType = pgEnum(
  'enum_visit_summary_upload_status_upload_type',
  ['FAX', 'SFTP']
);
export const visitSummaryUploadStatus = pgTable(
  'visit_summary_upload_status',
  {
    statusId: serial('status_id').primaryKey().notNull(),
    orderId: text('order_id').notNull(),
    userId: integer('user_id').notNull(),
    uploadType: enumVisitSummaryUploadStatusUploadType('upload_type').notNull(),
    uploadStatus: enumVisitSummaryUploadStatusUploadStatus('upload_status')
      .default('SUCCESS')
      .notNull(),
    error: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.orderId],
      name: 'visit_summary_upload_status_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'visit_summary_upload_status_user_id_fkey'
    })
  ]
);

export const visitSummaryUploadStatusRelations = relations(
  visitSummaryUploadStatus,
  ({ one }) => ({
    order: one(orders, {
      fields: [visitSummaryUploadStatus.orderId],
      references: [orders.orderId]
    }),
    user: one(users, {
      fields: [visitSummaryUploadStatus.userId],
      references: [users.userId]
    })
  })
);
