import { relations } from 'drizzle-orm';
import {
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { referrals } from './referrals';
import { telehealthServiceOrder } from './telehealth_service_order';

export const enumReferralTrackingShippingPartner = pgEnum(
  'enum_referral_tracking_shipping_partner',
  ['UPS', 'USPS', 'FedEx']
);
export const referralTracking = pgTable(
  'referral_tracking',
  {
    id: serial().primaryKey().notNull(),
    referralId: integer('referral_id'),
    orderGuid: varchar('order_guid', { length: 50 }),
    shippingPartner: enumReferralTrackingShippingPartner('shipping_partner'),
    trackingNumber: varchar('tracking_number', { length: 500 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.referralId],
      foreignColumns: [referrals.referralId],
      name: 'referral_tracking_referral_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'referral_tracking_order_guid_fkey'
    })
  ]
);

export const referralTrackingRelations = relations(
  referralTracking,
  ({ one }) => ({
    referral: one(referrals, {
      fields: [referralTracking.referralId],
      references: [referrals.referralId]
    }),
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [referralTracking.orderGuid],
      references: [telehealthServiceOrder.orderGuid]
    })
  })
);
