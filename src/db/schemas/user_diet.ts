import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { users } from './users';

export const enumUserDietMealType = pgEnum('enum_user_diet_meal_type', [
  'breakfast',
  'lunch',
  'dinner',
  'snacks'
]);
export const userDiet = pgTable(
  'user_diet',
  {
    userDietId: serial('user_diet_id').primaryKey().notNull(),
    userId: integer('user_id'),
    mealType: enumUserDietMealType('meal_type').notNull(),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    imageUrl: text('image_url'),
    serving: integer(),
    servingUnit: varchar('serving_unit', { length: 255 }),
    food: text(),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_diet_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_diet_user_id_fkey'
    })
  ]
);

export const userDietRelations = relations(userDiet, ({ one }) => ({
  order: one(orders, {
    fields: [userDiet.orderId],
    references: [orders.id]
  }),
  user: one(users, {
    fields: [userDiet.userId],
    references: [users.userId]
  })
}));
