import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { conversations } from './conversations';
import { users } from './users';

export const conversationMessages = pgTable(
  'conversation_messages',
  {
    cmId: serial('cm_id').primaryKey().notNull(),
    message: text(),
    userId: integer('user_id'),
    cId: integer('c_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.cId],
      foreignColumns: [conversations.cId],
      name: 'conversation_messages_c_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'conversation_messages_user_id_fkey'
    })
  ]
);

export const conversationMessagesRelations = relations(
  conversationMessages,
  ({ one }) => ({
    conversation: one(conversations, {
      fields: [conversationMessages.cId],
      references: [conversations.cId]
    }),
    user: one(users, {
      fields: [conversationMessages.userId],
      references: [users.userId]
    })
  })
);
