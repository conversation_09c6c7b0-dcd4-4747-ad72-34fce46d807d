import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const healthSummariesLog = pgTable(
  'health_summaries_log',
  {
    logId: serial('log_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    success: boolean().default(false),
    summaryDetails: text('summary_details'),
    step: varchar({ length: 255 }),
    message: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'health_summaries_log_user_id_fkey'
    })
  ]
);

export const healthSummariesLogRelations = relations(
  healthSummariesLog,
  ({ one }) => ({
    user: one(users, {
      fields: [healthSummariesLog.userId],
      references: [users.userId]
    })
  })
);
