import { relations } from 'drizzle-orm';
import {
  serial,
  pgEnum,
  pgTable,
  integer,
  boolean,
  foreignKey
} from 'drizzle-orm/pg-core';

import { states } from './states';
import { telehealthServices } from './telehealth_services';

export const enumTelehealthServiceStateMappingServiceType = pgEnum(
  'enum_telehealth_service_state_mapping_service_type',
  ['SYNC', 'ASYNC']
);

export const telehealthServiceStateMapping = pgTable(
  'telehealth_service_state_mapping',
  {
    id: serial().primaryKey().notNull(),
    stateId: integer('state_id').notNull(),
    serviceId: integer('service_id'),
    status: boolean().default(true),
    serviceType: enumTelehealthServiceStateMappingServiceType('service_type')
  },
  (table) => [
    foreignKey({
      columns: [table.stateId],
      foreignColumns: [states.stateId],
      name: 'telehealth_service_state_mapping_state_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_state_mapping_service_id_fkey'
    })
  ]
);

export const telehealthServiceStateMappingRelations = relations(
  telehealthServiceStateMapping,
  ({ one }) => ({
    state: one(states, {
      fields: [telehealthServiceStateMapping.stateId],
      references: [states.stateId]
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceStateMapping.serviceId],
      references: [telehealthServices.id]
    })
  })
);
