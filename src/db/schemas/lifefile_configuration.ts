import { relations } from 'drizzle-orm';
import {
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { pharmacies } from './pharmacies';
import { tennantMaster } from './tennant_master';

export const enumLifefileConfigurationShippingServices = pgEnum(
  'enum_lifefile_configuration_shipping_services',
  ['7780', '9']
);

export const lifefileConfiguration = pgTable(
  'lifefile_configuration',
  {
    lifefileConfigId: serial('lifefile_config_id').primaryKey().notNull(),
    tennantId: integer('tennant_id'),
    pharmacyId: integer('pharmacy_id'),
    pharmacyName: varchar('pharmacy_name', { length: 100 }),
    clientName: varchar('client_name', { length: 100 }),
    lifefileUrl: varchar('lifefile_url', { length: 200 }).notNull(),
    apiUsername: varchar('api_username', { length: 100 }).notNull(),
    apiPassword: varchar('api_password', { length: 100 }).notNull(),
    practiceId: integer('practice_id').notNull(),
    practiceName: varchar('practice_name', { length: 100 }).notNull(),
    vendorId: integer('vendor_id').notNull(),
    locationId: integer('location_id').notNull(),
    networkId: integer('network_id').notNull(),
    networkName: varchar('network_name', { length: 100 }).notNull(),
    shippingServices:
      enumLifefileConfigurationShippingServices('shipping_services').default(
        '7780'
      ),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'lifefile_configuration_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'lifefile_configuration_pharmacy_id_fkey'
    })
  ]
);

export const lifefileConfigurationRelations = relations(
  lifefileConfiguration,
  ({ one }) => ({
    tennantMaster: one(tennantMaster, {
      fields: [lifefileConfiguration.tennantId],
      references: [tennantMaster.id]
    }),
    pharmacy: one(pharmacies, {
      fields: [lifefileConfiguration.pharmacyId],
      references: [pharmacies.pharmacyId]
    })
  })
);
