import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const enumVitalsSummaryUploadStatusUploadStatus = pgEnum(
  'enum_vitals_summary_upload_status_upload_status',
  ['SUCCESS', 'FAILED']
);
export const enumVitalsSummaryUploadStatusUploadType = pgEnum(
  'enum_vitals_summary_upload_status_upload_type',
  ['FAX', 'SFTP']
);
export const vitalsSummaryUploadStatus = pgTable(
  'vitals_summary_upload_status',
  {
    statusId: serial('status_id').primaryKey().notNull(),
    doctorId: integer('doctor_id').notNull(),
    patientId: integer('patient_id').notNull(),
    uploadType:
      enumVitalsSummaryUploadStatusUploadType('upload_type').notNull(),
    uploadStatus: enumVitalsSummaryUploadStatusUploadStatus('upload_status')
      .default('SUCCESS')
      .notNull(),
    docPath: text('doc_path'),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    faxId: integer('fax_id'),
    faxStatus: text('fax_status'),
    error: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'vitals_summary_upload_status_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [users.userId],
      name: 'vitals_summary_upload_status_patient_id_fkey'
    })
  ]
);

export const vitalsSummaryUploadStatusRelations = relations(
  vitalsSummaryUploadStatus,
  ({ one }) => ({
    user_doctorId: one(users, {
      fields: [vitalsSummaryUploadStatus.doctorId],
      references: [users.userId],
      relationName: 'vitalsSummaryUploadStatus_doctorId_users_userId'
    }),
    user_patientId: one(users, {
      fields: [vitalsSummaryUploadStatus.patientId],
      references: [users.userId],
      relationName: 'vitalsSummaryUploadStatus_patientId_users_userId'
    })
  })
);
