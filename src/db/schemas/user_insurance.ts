import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userInsurance = pgTable(
  'user_insurance',
  {
    userInsuranceId: serial('user_insurance_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    insuranceMemberId: varchar('insurance_member_id', {
      length: 255
    }).notNull(),
    insurancePlanName: varchar('insurance_plan_name', {
      length: 255
    }).notNull(),
    payerIdentification: varchar('payer_identification', {
      length: 255
    }).notNull(),
    coverType: varchar('cover_type', { length: 255 }).notNull(),
    userInsuranceIdFront: varchar('user_insurance_id_front', { length: 255 }),
    userInsuranceIdBack: varchar('user_insurance_id_back', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_insurance_user_id_fkey'
    })
  ]
);

export const userInsuranceRelations = relations(userInsurance, ({ one }) => ({
  user: one(users, {
    fields: [userInsurance.userId],
    references: [users.userId]
  })
}));
