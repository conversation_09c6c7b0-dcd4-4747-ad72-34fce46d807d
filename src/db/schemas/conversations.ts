import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { conversationMessages } from './conversation_messages';
import { users } from './users';

export const conversations = pgTable(
  'conversations',
  {
    cId: serial('c_id').primaryKey().notNull(),
    userOne: integer('user_one'),
    userTwo: integer('user_two'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userOne],
      foreignColumns: [users.userId],
      name: 'conversations_user_one_fkey'
    }),
    foreignKey({
      columns: [table.userTwo],
      foreignColumns: [users.userId],
      name: 'conversations_user_two_fkey'
    })
  ]
);

export const conversationsRelations = relations(
  conversations,
  ({ one, many }) => ({
    conversationMessages: many(conversationMessages),
    user_userOne: one(users, {
      fields: [conversations.userOne],
      references: [users.userId],
      relationName: 'conversations_userOne_users_userId'
    }),
    user_userTwo: one(users, {
      fields: [conversations.userTwo],
      references: [users.userId],
      relationName: 'conversations_userTwo_users_userId'
    })
  })
);
