import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { users } from './users';

export const consultReassignHistory = pgTable(
  'consult_reassign_history',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id').notNull(),
    previousProvider: integer('previous_provider').notNull(),
    updatedProvider: integer('updated_provider').notNull(),
    reassignedBy: integer('reassigned_by').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_reassign_history_service_order_id_fkey'
    }),
    foreignKey({
      columns: [table.previousProvider],
      foreignColumns: [users.userId],
      name: 'consult_reassign_history_previous_provider_fkey'
    }),
    foreignKey({
      columns: [table.updatedProvider],
      foreignColumns: [users.userId],
      name: 'consult_reassign_history_updated_provider_fkey'
    }),
    foreignKey({
      columns: [table.reassignedBy],
      foreignColumns: [users.userId],
      name: 'consult_reassign_history_reassigned_by_fkey'
    })
  ]
);

export const consultReassignHistoryRelations = relations(
  consultReassignHistory,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [consultReassignHistory.serviceOrderId],
      references: [telehealthServiceOrder.id]
    }),
    user_previousProvider: one(users, {
      fields: [consultReassignHistory.previousProvider],
      references: [users.userId],
      relationName: 'consultReassignHistory_previousProvider_users_userId'
    }),
    user_updatedProvider: one(users, {
      fields: [consultReassignHistory.updatedProvider],
      references: [users.userId],
      relationName: 'consultReassignHistory_updatedProvider_users_userId'
    }),
    user_reassignedBy: one(users, {
      fields: [consultReassignHistory.reassignedBy],
      references: [users.userId],
      relationName: 'consultReassignHistory_reassignedBy_users_userId'
    })
  })
);
