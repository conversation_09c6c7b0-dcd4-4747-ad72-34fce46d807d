import { sql, relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { telehealthServiceOrder } from './telehealth_service_order';

export const consultNotes = pgTable(
  'consult_notes',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id').notNull(),
    icdCode: text('icd_code').default(''),
    cptCode: text('cpt_code').default(''),
    subjective: text().default(''),
    objective: text().default(''),
    assessment: text().default(''),
    plan: text().default(''),
    ermId: varchar('erm_id', { length: 150 }).default(''),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    intervention: text(),
    outcome: text(),
    goal: text(),
    orderGuid: varchar('order_guid', { length: 50 }),
    generalNote: varchar('general_note', { length: 2000 }).default(sql`NULL`)
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'consult_notes_order_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'consult_notes_order_guid_fkey'
    })
  ]
);

export const consultNotesRelations = relations(consultNotes, ({ one }) => ({
  order: one(orders, {
    fields: [consultNotes.orderId],
    references: [orders.id]
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [consultNotes.orderGuid],
    references: [telehealthServiceOrder.orderGuid]
  })
}));
