import { relations } from 'drizzle-orm';
import { text, serial, pgTable, timestamp } from 'drizzle-orm/pg-core';

import { userForms } from './user_forms';

export const forms = pgTable('forms', {
  formId: serial('form_id').primaryKey().notNull(),
  name: text().notNull(),
  url: text(),
  data: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const formsRelations = relations(forms, ({ many }) => ({
  userForms: many(userForms)
}));
