import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { pharmacyStateServiceMapping } from './pharmacy_state_service_mapping';
import { providerLicense } from './provider_license';
import { telehealthServiceStateMapping } from './telehealth_service_state_mapping';
import { userLicenses } from './user_licenses';

export const states = pgTable('states', {
  stateId: serial('state_id').primaryKey().notNull(),
  name: varchar({ length: 50 }).notNull(),
  abbreviation: varchar({ length: 2 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  isAsync: boolean('is_async').default(true).notNull()
});

export const statesRelations = relations(states, ({ many }) => ({
  providerLicenses: many(providerLicense),
  telehealthServiceStateMappings: many(telehealthServiceStateMapping),
  pharmacyStateServiceMappings: many(pharmacyStateServiceMapping),
  userLicenses: many(userLicenses)
}));
