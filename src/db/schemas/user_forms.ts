import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { forms } from './forms';
import { orders } from './orders';
import { users } from './users';

export const userForms = pgTable(
  'user_forms',
  {
    userFormId: serial('user_form_id').primaryKey().notNull(),
    formId: integer('form_id'),
    assignedBy: integer('assigned_by'),
    assignedTo: integer('assigned_to'),
    doctorId: integer('doctor_id'),
    filledFormUrl: text('filled_form_url'),
    data: text(),
    status: varchar({ length: 255 }),
    score: numeric(),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.assignedBy],
      foreignColumns: [users.userId],
      name: 'user_forms_assigned_by_fkey'
    }),
    foreignKey({
      columns: [table.assignedTo],
      foreignColumns: [users.userId],
      name: 'user_forms_assigned_to_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_forms_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.formId],
      foreignColumns: [forms.formId],
      name: 'user_forms_form_id_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_forms_order_id_fkey'
    })
  ]
);

export const userFormsRelations = relations(userForms, ({ one }) => ({
  user_assignedBy: one(users, {
    fields: [userForms.assignedBy],
    references: [users.userId],
    relationName: 'userForms_assignedBy_users_userId'
  }),
  user_assignedTo: one(users, {
    fields: [userForms.assignedTo],
    references: [users.userId],
    relationName: 'userForms_assignedTo_users_userId'
  }),
  user_doctorId: one(users, {
    fields: [userForms.doctorId],
    references: [users.userId],
    relationName: 'userForms_doctorId_users_userId'
  }),
  form: one(forms, {
    fields: [userForms.formId],
    references: [forms.formId]
  }),
  order: one(orders, {
    fields: [userForms.orderId],
    references: [orders.id]
  })
}));
