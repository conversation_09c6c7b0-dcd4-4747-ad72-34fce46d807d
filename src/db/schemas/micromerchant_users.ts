import { text, serial, pgTable, varchar, timestamp } from 'drizzle-orm/pg-core';

export const micromerchantUsers = pgTable('micromerchant_users', {
  mmUserId: serial('mm_user_id').primaryKey().notNull(),
  firstName: text('first_name'),
  lastName: text('last_name'),
  zipCode: varchar('zip_code', { length: 255 }),
  email: text().notNull(),
  phone: varchar({ length: 255 }),
  gender: varchar({ length: 255 }),
  data: text(),
  dob: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
