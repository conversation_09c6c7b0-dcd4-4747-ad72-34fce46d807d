import { sql, relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { requestObjects } from './request_objects';
import { requestTranslation } from './request_translation';
import { telehealthServiceOrder } from './telehealth_service_order';
import { users } from './users';

export const enumRequestsStatus = pgEnum('enum_requests_status', [
  'open',
  'accepted',
  'rejected'
]);

export const requests = pgTable(
  'requests',
  {
    requestId: serial('request_id').primaryKey().notNull(),
    requestorId: integer('requestor_id').notNull(),
    requesteeId: integer('requestee_id').notNull(),
    objectId: integer('object_id').notNull(),
    status: enumRequestsStatus().default('open'),
    message: varchar({ length: 255 }),
    detail: text(),
    patientHistory: text('patient_history'),
    requestorReadStatus: boolean('requestor_read_status').default(false),
    requesteeReadStatus: boolean('requestee_read_status').default(false),
    entityId: integer('entity_id'),
    addPracticeGroupDoctors: boolean('add_practice_group_doctors').default(
      false
    ),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    releaseMedical: boolean('release_medical').default(false),
    rescheduled: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.objectId],
      foreignColumns: [requestObjects.objectId],
      name: 'requests_object_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'requests_order_guid_fkey'
    }),
    foreignKey({
      columns: [table.requesteeId],
      foreignColumns: [users.userId],
      name: 'requests_requestee_id_fkey'
    }),
    foreignKey({
      columns: [table.requestorId],
      foreignColumns: [users.userId],
      name: 'requests_requestor_id_fkey'
    })
  ]
);

export const requestsRelations = relations(requests, ({ one, many }) => ({
  requestObject: one(requestObjects, {
    fields: [requests.objectId],
    references: [requestObjects.objectId]
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [requests.orderGuid],
    references: [telehealthServiceOrder.orderGuid]
  }),
  user_requesteeId: one(users, {
    fields: [requests.requesteeId],
    references: [users.userId],
    relationName: 'requests_requesteeId_users_userId'
  }),
  user_requestorId: one(users, {
    fields: [requests.requestorId],
    references: [users.userId],
    relationName: 'requests_requestorId_users_userId'
  }),
  requestTranslations: many(requestTranslation)
}));
