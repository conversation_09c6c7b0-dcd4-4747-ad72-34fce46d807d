import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userExternalIdMapping = pgTable(
  'user_external_id_mapping',
  {
    userExternalIdMappingId: serial('user_external_id_mapping_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id').notNull(),
    externalId: varchar('external_id', { length: 255 }).notNull(),
    externalIdSource: varchar('external_id_source', { length: 255 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_external_id_mapping_user_id_fkey'
    })
  ]
);

export const userExternalIdMappingRelations = relations(
  userExternalIdMapping,
  ({ one }) => ({
    user: one(users, {
      fields: [userExternalIdMapping.userId],
      references: [users.userId]
    })
  })
);
