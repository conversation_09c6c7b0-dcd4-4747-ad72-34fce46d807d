import { text, pgTable, timestamp } from 'drizzle-orm/pg-core';

export const tokboxArchiveType = pgTable('tokbox_archive_type', {
  sessionId: text('session_id').primaryKey().notNull(),
  archiveId: text('archive_id'),
  type: text().notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
