import { relations } from 'drizzle-orm';
import {
  text,
  uuid,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { users } from './users';

export const supportNotes = pgTable(
  'support_notes',
  {
    id: uuid().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    supportUserId: integer('support_user_id').notNull(),
    orderGuid: varchar('order_guid', { length: 50 }),
    generalNote: text('general_note').default(''),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'support_notes_user_id_fkey'
    }),
    foreignKey({
      columns: [table.supportUserId],
      foreignColumns: [users.userId],
      name: 'support_notes_support_user_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'support_notes_order_guid_fkey'
    })
  ]
);

export const supportNotesRelations = relations(supportNotes, ({ one }) => ({
  user_userId: one(users, {
    fields: [supportNotes.userId],
    references: [users.userId],
    relationName: 'supportNotes_userId_users_userId'
  }),
  user_supportUserId: one(users, {
    fields: [supportNotes.supportUserId],
    references: [users.userId]
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [supportNotes.orderGuid],
    references: [telehealthServiceOrder.orderGuid]
  })
}));
