import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { enumUserFileRepoDetailsUploadType } from './user_files';
import { users } from './users';

export const userFileRepoDetails = pgTable(
  'user_file_repo_details',
  {
    repoId: serial('repo_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    uploadType: enumUserFileRepoDetailsUploadType('upload_type').notNull(),
    connectionDetails: text('connection_details'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_file_repo_details_user_id_fkey'
    })
  ]
);

export const userFileRepoDetailsRelations = relations(
  userFileRepoDetails,
  ({ one }) => ({
    user: one(users, {
      fields: [userFileRepoDetails.userId],
      references: [users.userId]
    })
  })
);
