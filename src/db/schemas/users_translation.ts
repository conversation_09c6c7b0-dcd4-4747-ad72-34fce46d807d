import { relations } from 'drizzle-orm';
import {
  text,
  pgTable,
  varchar,
  integer,
  foreignKey,
  primaryKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const usersTranslation = pgTable(
  'users_translation',
  {
    userId: integer('user_id').notNull(),
    languageCode: varchar('language_code', { length: 255 }).notNull(),
    firstName: text('first_name'),
    lastName: text('last_name'),
    history: text(),
    questionnaire: text()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'users_translation_user_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.languageCode],
      name: 'users_translation_pkey'
    })
  ]
);

export const usersTranslationRelations = relations(
  usersTranslation,
  ({ one }) => ({
    user: one(users, {
      fields: [usersTranslation.userId],
      references: [users.userId]
    })
  })
);
