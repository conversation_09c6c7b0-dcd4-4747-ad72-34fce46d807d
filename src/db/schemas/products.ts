import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { referrals } from './referrals';

export const products = pgTable('products', {
  productId: serial('product_id').primaryKey().notNull(),
  productName: varchar('product_name', { length: 200 }).notNull(),
  description: varchar({ length: 2000 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false)
});

export const productsRelations = relations(products, ({ many }) => ({
  referrals: many(referrals)
}));
