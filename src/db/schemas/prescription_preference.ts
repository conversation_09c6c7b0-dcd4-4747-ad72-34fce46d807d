import { relations } from 'drizzle-orm';
import {
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { pharmacies } from './pharmacies';
import { tennantMaster } from './tennant_master';

export const enumPrescriptionPreferencePreference = pgEnum(
  'enum_prescription_preference_preference',
  ['fax', 'life_file', 'doespot', 'wellsync-pharmacy-hub']
);

export const prescriptionPreference = pgTable(
  'prescription_preference',
  {
    preferenceId: serial('preference_id').primaryKey().notNull(),
    pharmacyId: integer('pharmacy_id'),
    tennantId: integer('tennant_id'),
    pharmacyName: varchar('pharmacy_name', { length: 100 }),
    clientName: varchar('client_name', { length: 100 }),
    preference: enumPrescriptionPreferencePreference().default('fax'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'prescription_preference_pharmacy_id_fkey'
    }),
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'prescription_preference_tennant_id_fkey'
    })
  ]
);

export const prescriptionPreferenceRelations = relations(
  prescriptionPreference,
  ({ one }) => ({
    pharmacy: one(pharmacies, {
      fields: [prescriptionPreference.pharmacyId],
      references: [pharmacies.pharmacyId]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [prescriptionPreference.tennantId],
      references: [tennantMaster.id]
    })
  })
);
