import { sql, relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { scheduleTranslation } from './schedule_translation';
import { telehealthServiceOrder } from './telehealth_service_order';
import { users } from './users';

export const schedules = pgTable(
  'schedules',
  {
    scheduleId: serial('schedule_id').primaryKey().notNull(),
    scheduledWith: integer('scheduled_with'),
    scheduledBy: integer('scheduled_by'),
    startYear: integer('start_year').notNull(),
    startMonth: integer('start_month').notNull(),
    startDay: integer('start_day').notNull(),
    endYear: integer('end_year').notNull(),
    endMonth: integer('end_month').notNull(),
    endDay: integer('end_day').notNull(),
    start: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    end: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    detail: text(),
    patientHistory: text('patient_history'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    releaseMedical: boolean('release_medical').default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    orderId: integer('order_id')
  },
  (table) => [
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'schedules_order_guid_fkey'
    }),
    foreignKey({
      columns: [table.scheduledBy],
      foreignColumns: [users.userId],
      name: 'schedules_scheduled_by_fkey'
    }),
    foreignKey({
      columns: [table.scheduledWith],
      foreignColumns: [users.userId],
      name: 'schedules_scheduled_with_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'schedules_order_id_fkey'
    })
  ]
);

export const schedulesRelations = relations(schedules, ({ one, many }) => ({
  orders: many(orders),
  telehealthServiceOrder_orderGuid: one(telehealthServiceOrder, {
    fields: [schedules.orderGuid],
    references: [telehealthServiceOrder.orderGuid],
    relationName: 'schedules_orderGuid_telehealthServiceOrder_orderGuid'
  }),
  user_scheduledBy: one(users, {
    fields: [schedules.scheduledBy],
    references: [users.userId],
    relationName: 'schedules_scheduledBy_users_userId'
  }),
  user_scheduledWith: one(users, {
    fields: [schedules.scheduledWith],
    references: [users.userId],
    relationName: 'schedules_scheduledWith_users_userId'
  }),
  telehealthServiceOrder_orderId: one(telehealthServiceOrder, {
    fields: [schedules.orderId],
    references: [telehealthServiceOrder.id],
    relationName: 'schedules_orderId_telehealthServiceOrder_id'
  }),
  scheduleTranslations: many(scheduleTranslation)
}));
