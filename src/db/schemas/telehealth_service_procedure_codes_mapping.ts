import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { procedureCodes } from './procedure_codes';
import { telehealthServices } from './telehealth_services';

export const telehealthServiceProcedureCodesMapping = pgTable(
  'telehealth_service_procedure_codes_mapping',
  {
    telehealthServiceProcedureCodesMappingId: serial(
      'telehealth_service_procedure_codes_mapping_id'
    )
      .primaryKey()
      .notNull(),
    serviceId: integer('service_id').notNull(),
    procedureCodeId: integer('procedure_code_id').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_procedure_codes_mapping_service_id_fkey'
    }),
    foreignKey({
      columns: [table.procedureCodeId],
      foreignColumns: [procedureCodes.procedureCodeId],
      name: 'telehealth_service_procedure_codes_mappi_procedure_code_id_fkey'
    })
  ]
);

export const telehealthServiceProcedureCodesMappingRelations = relations(
  telehealthServiceProcedureCodesMapping,
  ({ one }) => ({
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceProcedureCodesMapping.serviceId],
      references: [telehealthServices.id]
    }),
    procedureCode: one(procedureCodes, {
      fields: [telehealthServiceProcedureCodesMapping.procedureCodeId],
      references: [procedureCodes.procedureCodeId]
    })
  })
);
