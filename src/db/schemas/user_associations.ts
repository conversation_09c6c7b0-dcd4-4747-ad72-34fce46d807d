import { relations } from 'drizzle-orm';
import {
  text,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey,
  primaryKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userAssociations = pgTable(
  'user_associations',
  {
    userId: integer('user_id').notNull(),
    buserId: integer('buser_id').notNull(),
    userFavorite: boolean('user_favorite').default(false),
    buserFavorite: boolean('buser_favorite').default(false),
    maManageOrders: boolean('ma_manage_orders').default(false),
    isNotifyOnCapture: boolean('is_notify_on_capture').default(false),
    isRpmEnabled: boolean('is_rpm_enabled').default(false),
    isCustomizedVitalsThresholds: boolean(
      'is_customized_vitals_thresholds'
    ).default(false),
    vitalsThresholds: text('vitals_thresholds'),
    vitalsCron: text('vitals_cron'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.buserId],
      foreignColumns: [users.userId],
      name: 'user_associations_buser_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_associations_user_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.buserId],
      name: 'user_associations_pkey'
    })
  ]
);

export const userAssociationsRelations = relations(
  userAssociations,
  ({ one }) => ({
    user_buserId: one(users, {
      fields: [userAssociations.buserId],
      references: [users.userId],
      relationName: 'userAssociations_buserId_users_userId'
    }),
    user_userId: one(users, {
      fields: [userAssociations.userId],
      references: [users.userId],
      relationName: 'userAssociations_userId_users_userId'
    })
  })
);
