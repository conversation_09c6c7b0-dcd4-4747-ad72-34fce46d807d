import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { users } from './users';

export const consultUpdateDetailsHistory = pgTable(
  'consult_update_details_history',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id'),
    userId: integer('user_id').notNull(),
    previousValues: varchar('previous_values', { length: 2000 }),
    updatedValues: varchar('updated_values', { length: 2000 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'consult_update_details_history_user_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey1'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey2'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey3'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey4'
    })
  ]
);

export const consultUpdateDetailsHistoryRelations = relations(
  consultUpdateDetailsHistory,
  ({ one }) => ({
    telehealthServiceOrder_serviceOrderId: one(telehealthServiceOrder, {
      fields: [consultUpdateDetailsHistory.serviceOrderId],
      references: [telehealthServiceOrder.id],
      relationName:
        'consultUpdateDetailsHistory_serviceOrderId_telehealthServiceOrder_id'
    }),
    user: one(users, {
      fields: [consultUpdateDetailsHistory.userId],
      references: [users.userId]
    })
  })
);
