import { relations } from 'drizzle-orm';
import {
  index,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';

export const refillRequest = pgTable(
  'refill_request',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id').notNull(),
    drugDetails: varchar('drug_details', { length: 5000 }).notNull(),
    prescriptionImages: varchar('prescription_images', {
      length: 5000
    }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    index('refill_request_drug_details').using(
      'btree',
      table.drugDetails.asc().nullsLast().op('text_ops')
    ),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'refill_request_service_order_id_fkey'
    })
  ]
);

export const refillRequestRelations = relations(refillRequest, ({ one }) => ({
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [refillRequest.serviceOrderId],
    references: [telehealthServiceOrder.id]
  })
}));
