import { relations } from 'drizzle-orm';
import {
  text,
  pgEnum,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const enumStripeUserDetailsCcStatus = pgEnum(
  'enum_stripe_user_details_cc_status',
  ['captured', 'not_captured', 'payment_error', 'capture_immediate']
);
export const enumStripeUserDetailsOauthStatus = pgEnum(
  'enum_stripe_user_details_oauth_status',
  ['connected', 'not_connected', 'payouts_disabled']
);

export const stripeUserDetails = pgTable(
  'stripe_user_details',
  {
    userId: integer('user_id').primaryKey().notNull(),
    stripeUserId: text('stripe_user_id').notNull(),
    ccStatus:
      enumStripeUserDetailsCcStatus('cc_status').default('not_captured'),
    oauthStatus:
      enumStripeUserDetailsOauthStatus('oauth_status').default('not_connected'),
    stripeAccountId: text('stripe_account_id'),
    oauthVerificationToken: text('oauth_verification_token'),
    currencyCode: text('currency_code'),
    defaultPaymentMethod: text('default_payment_method'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'stripe_user_details_user_id_fkey'
    })
  ]
);

export const stripeUserDetailsRelations = relations(
  stripeUserDetails,
  ({ one }) => ({
    user: one(users, {
      fields: [stripeUserDetails.userId],
      references: [users.userId]
    })
  })
);
