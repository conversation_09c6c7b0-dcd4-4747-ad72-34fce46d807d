import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { referrals } from './referrals';
import { users } from './users';

export const faxes = pgTable(
  'faxes',
  {
    faxId: serial('fax_id').primaryKey().notNull(),
    faxSid: text('fax_sid').notNull(),
    faxNumber: text('fax_number').notNull(),
    status: text().notNull(),
    referralId: integer('referral_id'),
    detail: text(),
    mediaUrl: text('media_url'),
    sentBy: integer('sent_by'),
    sentFor: integer('sent_for'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.referralId],
      foreignColumns: [referrals.referralId],
      name: 'faxes_referral_id_fkey'
    }),
    foreignKey({
      columns: [table.sentBy],
      foreignColumns: [users.userId],
      name: 'faxes_sent_by_fkey'
    }),
    foreignKey({
      columns: [table.sentFor],
      foreignColumns: [users.userId],
      name: 'faxes_sent_for_fkey'
    })
  ]
);

export const faxesRelations = relations(faxes, ({ one }) => ({
  referral: one(referrals, {
    fields: [faxes.referralId],
    references: [referrals.referralId]
  }),
  user_sentBy: one(users, {
    fields: [faxes.sentBy],
    references: [users.userId],
    relationName: 'faxes_sentBy_users_userId'
  }),
  user_sentFor: one(users, {
    fields: [faxes.sentFor],
    references: [users.userId],
    relationName: 'faxes_sentFor_users_userId'
  })
}));
