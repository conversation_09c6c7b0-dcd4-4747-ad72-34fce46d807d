import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const healthSummariesSchedule = pgTable(
  'health_summaries_schedule',
  {
    scheduleId: serial('schedule_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    nextRunDate: timestamp('next_run_date', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'health_summaries_schedule_user_id_fkey'
    })
  ]
);

export const healthSummariesScheduleRelations = relations(
  healthSummariesSchedule,
  ({ one }) => ({
    user: one(users, {
      fields: [healthSummariesSchedule.userId],
      references: [users.userId]
    })
  })
);
