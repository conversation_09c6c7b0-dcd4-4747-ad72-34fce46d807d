import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceQuestionAnswer } from './telehealth_service_question_answer';
import { telehealthServices } from './telehealth_services';

export const enumTelehealthServiceQuestionsQuestionFor = pgEnum(
  'enum_telehealth_service_questions_question_for',
  ['male', 'female', 'both', 'provider']
);
export const enumTelehealthServiceQuestionsQuestionType = pgEnum(
  'enum_telehealth_service_questions_question_type',
  [
    'YesNo',
    'Text',
    'Selection',
    'MultipleSelection',
    'Date',
    'DateTime',
    'TextArea',
    'Height',
    'Weight',
    'Bmi',
    'FileUpload',
    'HeightWeightBmi'
  ]
);

export const telehealthServiceQuestions = pgTable(
  'telehealth_service_questions',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    question: varchar({ length: 500 }).notNull(),
    helpText: varchar('help_text', { length: 200 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    selectionOption: text('selection_option'),
    questionType:
      enumTelehealthServiceQuestionsQuestionType('question_type').default(
        'YesNo'
      ),
    displayOrder: integer('display_order').default(1),
    parentId: integer('parent_id'),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    haltOnSelectionOption: varchar('halt_on_selection_option', { length: 500 }),
    isPreQuestions: boolean('is_pre_questions').default(false),
    isOptional: boolean('is_optional').default(false),
    questionFor:
      enumTelehealthServiceQuestionsQuestionFor('question_for').default('both')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_questions_service_id_fkey'
    })
  ]
);

export const telehealthServiceQuestionsRelations = relations(
  telehealthServiceQuestions,
  ({ one, many }) => ({
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceQuestions.serviceId],
      references: [telehealthServices.id]
    }),
    telehealthServiceQuestionAnswers: many(telehealthServiceQuestionAnswer)
  })
);
