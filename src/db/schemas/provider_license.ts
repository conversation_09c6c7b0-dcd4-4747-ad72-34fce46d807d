import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { states } from './states';
import { users } from './users';

export const providerLicense = pgTable(
  'provider_license',
  {
    providerLicenseId: serial('provider_license_id').primaryKey().notNull(),
    userId: integer('user_id'),
    licenseNumber: varchar('license_number', { length: 255 }),
    licenseState: integer('license_state'),
    licenseStateName: varchar('license_state_name', { length: 255 }),
    licenseExpirationDate: timestamp('license_expiration_date', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'provider_license_user_id_fkey'
    }),
    foreignKey({
      columns: [table.licenseState],
      foreignColumns: [states.stateId],
      name: 'provider_license_license_state_fkey'
    })
  ]
);

export const providerLicenseRelations = relations(
  providerLicense,
  ({ one }) => ({
    user: one(users, {
      fields: [providerLicense.userId],
      references: [users.userId]
    }),
    state: one(states, {
      fields: [providerLicense.licenseState],
      references: [states.stateId]
    })
  })
);
