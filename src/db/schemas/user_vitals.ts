import { relations } from 'drizzle-orm';
import {
  text,
  index,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { users } from './users';

export const enumUserVitalsMode = pgEnum('enum_user_vitals_mode', [
  'automated',
  'doctor',
  'patient'
]);

export const userVitals = pgTable(
  'user_vitals',
  {
    userVitalId: serial('user_vital_id').primaryKey().notNull(),
    userId: integer('user_id'),
    value: text(),
    metric: varchar({ length: 255 }),
    detail: text(),
    type: varchar({ length: 255 }),
    displayName: varchar('display_name', { length: 255 }),
    mode: enumUserVitalsMode().default('automated'),
    billed: boolean().default(false),
    procedure: text(),
    abnormal: boolean().default(false),
    entityId: integer('entity_id'),
    bundleId: varchar('bundle_id', { length: 255 }),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    description: text(),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    currency: text(),
    cost: numeric(),
    sourcePlatform: varchar('source_platform', { length: 255 })
  },
  (table) => [
    index('idx_user_vitals_user_id_date').using(
      'btree',
      table.userId.asc().nullsLast().op('int4_ops'),
      table.date.desc().nullsFirst().op('int4_ops')
    ),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_vitals_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_vitals_user_id_fkey'
    })
  ]
);

export const userVitalsRelations = relations(userVitals, ({ one }) => ({
  order: one(orders, {
    fields: [userVitals.orderId],
    references: [orders.id]
  }),
  user: one(users, {
    fields: [userVitals.userId],
    references: [users.userId]
  })
}));
