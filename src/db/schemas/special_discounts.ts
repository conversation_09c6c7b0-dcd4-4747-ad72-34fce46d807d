import { text, serial, pgTable, integer, timestamp } from 'drizzle-orm/pg-core';

export const specialDiscounts = pgTable('special_discounts', {
  discountId: serial('discount_id').primaryKey().notNull(),
  name: text().notNull(),
  description: text(),
  discountPrice: integer('discount_price').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
