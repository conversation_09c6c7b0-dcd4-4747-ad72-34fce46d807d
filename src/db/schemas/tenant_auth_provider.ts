import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { tennantMaster } from './tennant_master';

export const tenantAuthProvider = pgTable(
  'tenant_auth_provider',
  {
    authId: serial('auth_id').primaryKey().notNull(),
    tenantId: integer('tenant_id').notNull(),
    secretToken: varchar('secret_token', { length: 255 }).notNull(),
    authToken: varchar('auth_token', { length: 255 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'tenant_auth_provider_tenant_id_fkey'
    })
  ]
);

export const tenantAuthProviderRelations = relations(
  tenantAuthProvider,
  ({ one }) => ({
    tennantMaster: one(tennantMaster, {
      fields: [tenantAuthProvider.tenantId],
      references: [tennantMaster.id]
    })
  })
);
