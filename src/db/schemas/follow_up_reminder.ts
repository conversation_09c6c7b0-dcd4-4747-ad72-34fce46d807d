import { relations } from 'drizzle-orm';
import {
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { tennantMaster } from './tennant_master';
import { users } from './users';

export const enumFollowUpReminderStatus = pgEnum(
  'enum_follow_up_reminder_status',
  ['pending', 'started', 'failed', 'sent', 'cancel', 'cancelled_by_admin']
);

export const followUpReminder = pgTable(
  'follow_up_reminder',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id'),
    userId: integer('user_id'),
    lastFollowUpSent: varchar('last_follow_up_sent', { length: 50 }),
    errorMessage: varchar('error_message', { length: 200 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    nextFollowUp: timestamp('next_follow_up', {
      withTimezone: true,
      mode: 'string'
    }),
    status: enumFollowUpReminderStatus().default('pending'),
    tennantId: integer('tennant_id'),
    nextOrderId: integer('next_order_id'),
    followUpSentDate: timestamp('follow_up_sent_date', {
      withTimezone: true,
      mode: 'string'
    })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'follow_up_reminder_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'follow_up_reminder_user_id_fkey'
    }),
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'follow_up_reminder_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.nextOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'follow_up_reminder_next_order_id_fkey'
    })
  ]
);

export const followUpReminderRelations = relations(
  followUpReminder,
  ({ one }) => ({
    telehealthServiceOrder_orderId: one(telehealthServiceOrder, {
      fields: [followUpReminder.orderId],
      references: [telehealthServiceOrder.id],
      relationName: 'followUpReminder_orderId_telehealthServiceOrder_id'
    }),
    user: one(users, {
      fields: [followUpReminder.userId],
      references: [users.userId]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [followUpReminder.tennantId],
      references: [tennantMaster.id]
    }),
    telehealthServiceOrder_nextOrderId: one(telehealthServiceOrder, {
      fields: [followUpReminder.nextOrderId],
      references: [telehealthServiceOrder.id],
      relationName: 'followUpReminder_nextOrderId_telehealthServiceOrder_id'
    })
  })
);
