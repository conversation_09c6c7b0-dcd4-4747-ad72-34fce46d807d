import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { chatRooms } from './chat_rooms';
import { users } from './users';

export const chatMessages = pgTable(
  'chat_messages',
  {
    id: serial().primaryKey().notNull(),
    message: text(),
    senderId: serial('sender_id').notNull(),
    roomId: serial('room_id').notNull(),
    deleted: boolean().default(false).notNull(),
    deletedAt: timestamp('deleted_at', { mode: 'string' }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    fileId: integer('file_id'),
    readBy: text('read_by').array().default(['RAY']).notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.senderId],
      foreignColumns: [users.userId],
      name: 'chat_messages_sender_id_users_user_id_fk'
    }),
    foreignKey({
      columns: [table.roomId],
      foreignColumns: [chatRooms.id],
      name: 'chat_messages_room_id_chat_rooms_id_fk'
    })
  ]
);

export const chatMessagesRelations = relations(chatMessages, ({ one }) => ({
  user: one(users, {
    fields: [chatMessages.senderId],
    references: [users.userId]
  }),
  chatRoom: one(chatRooms, {
    fields: [chatMessages.roomId],
    references: [chatRooms.id]
  })
}));
