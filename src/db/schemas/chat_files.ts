import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { chatRooms } from './chat_rooms';
import { users } from './users';

export const chatFiles = pgTable(
  'chat_files',
  {
    fileId: serial('file_id').primaryKey().notNull(),
    userId: serial('user_id').notNull(),
    roomId: serial('room_id').notNull(),
    name: text(),
    path: text(),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'chat_files_user_id_users_user_id_fk'
    }),
    foreignKey({
      columns: [table.roomId],
      foreignColumns: [chatRooms.id],
      name: 'chat_files_room_id_ chat_rooms_id_fk'
    })
  ]
);

export const chatFilesRelations = relations(chatFiles, ({ one }) => ({
  user: one(users, {
    fields: [chatFiles.userId],
    references: [users.userId]
  }),
  chatRoom: one(chatRooms, {
    fields: [chatFiles.roomId],
    references: [chatRooms.id]
  })
}));
