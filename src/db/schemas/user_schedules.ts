import { relations } from 'drizzle-orm';
import {
  uuid,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userSchedules = pgTable(
  'user_schedules',
  {
    id: uuid().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    isAvailable: boolean('is_available').default(true),
    endDatetime: timestamp('end_datetime', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    createdBy: integer('created_by'),
    updatedBy: integer('updated_by'),
    deletedBy: integer('deleted_by'),
    scheduleDate: varchar('schedule_date', { length: 30 })
      .default('')
      .notNull(),
    startDatetime: timestamp('start_datetime', {
      withTimezone: true,
      mode: 'string'
    })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_schedules_user_id_fkey'
    }),
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'user_schedules_created_by_fkey'
    }),
    foreignKey({
      columns: [table.updatedBy],
      foreignColumns: [users.userId],
      name: 'user_schedules_updated_by_fkey'
    }),
    foreignKey({
      columns: [table.deletedBy],
      foreignColumns: [users.userId],
      name: 'user_schedules_deleted_by_fkey'
    })
  ]
);

export const userSchedulesRelations = relations(userSchedules, ({ one }) => ({
  user_userId: one(users, {
    fields: [userSchedules.userId],
    references: [users.userId],
    relationName: 'userSchedules_userId_users_userId'
  }),
  user_createdBy: one(users, {
    fields: [userSchedules.createdBy],
    references: [users.userId],
    relationName: 'userSchedules_createdBy_users_userId'
  }),
  user_updatedBy: one(users, {
    fields: [userSchedules.updatedBy],
    references: [users.userId],
    relationName: 'userSchedules_updatedBy_users_userId'
  }),
  user_deletedBy: one(users, {
    fields: [userSchedules.deletedBy],
    references: [users.userId],
    relationName: 'userSchedules_deletedBy_users_userId'
  })
}));
