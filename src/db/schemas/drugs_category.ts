import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { drugs } from './drugs';

export const drugsCategory = pgTable('drugs_category', {
  categoryId: serial('category_id').primaryKey().notNull(),
  categoryName: varchar('category_name', { length: 200 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false)
});

export const drugsCategoryRelations = relations(drugsCategory, ({ many }) => ({
  drugs: many(drugs)
}));
