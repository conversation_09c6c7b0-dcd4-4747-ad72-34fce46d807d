import { sql, relations } from 'drizzle-orm';
import {
  text,
  uuid,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { consultOrderFiles } from './consult_order_files';
import { followUpReminder } from './follow_up_reminder';
import { lifefileConfiguration } from './lifefile_configuration';
import { prescriptionPreference } from './prescription_preference';
import { serviceActionPreference } from './service_action_preference';
import { telehealthServiceCategory } from './telehealth_service_category';
import { telehealthServiceMaster } from './telehealth_service_master';
import { tenantAuthProvider } from './tenant_auth_provider';
import { tenantFiles } from './tenant_files';
import { tennantConfig } from './tennant_config';
import { userFiles } from './user_files';
import { users } from './users';

export const enumTennantMasterPreferredPaymentGateway = pgEnum(
  'enum_tennant_master_preferred_payment_gateway',
  ['PAYPAL', 'STRIPE']
);

export const tennantMaster: any = pgTable(
  'tennant_master',
  {
    id: serial().primaryKey().notNull(),
    tennantName: varchar('tennant_name', { length: 255 }).notNull(),
    pharmacyFax: varchar('pharmacy_fax', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    filterDisplay: boolean('filter_display').default(false),
    parentId: integer('parent_id'),
    labFaxNumber: varchar('lab_fax_number', { length: 255 }),
    supportEmail: varchar('support_email', { length: 255 }),
    logoUrl: varchar('logo_url', { length: 255 }),
    paypalClientId: text('paypal_client_id'),
    paypalClientSecret: text('paypal_client_secret'),
    accessLabClientNumber: varchar('access_lab_client_number', {
      length: 2000
    }),
    twilioAccountSid: text('twilio_account_sid'),
    twilioPhoneNumber: text('twilio_phone_number'),
    twilioAuthToken: text('twilio_auth_token'),
    stripeClientId: varchar('stripe_client_id', { length: 2000 }),
    stripeClientSecret: varchar('stripe_client_secret', { length: 2000 }),
    preferredPaymentGateway: enumTennantMasterPreferredPaymentGateway(
      'preferred_payment_gateway'
    ),
    mailchipFromEmail: varchar('mailchip_from_email', { length: 255 }),
    mailchipFromEmailName: varchar('mailchip_from_email_name', { length: 255 }),
    tennantDisplayName: varchar('tennant_display_name', { length: 255 }),
    tenantAccess: varchar('tenant_access', { length: 500 }),
    dosespotClientId: varchar('dosespot_client_id', { length: 1000 }),
    dosespotClientSecret: varchar('dosespot_client_secret', { length: 1000 }),
    configuredDomain: varchar('configured_domain', { length: 200 }),
    tennantGuid: uuid('tennant_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    defaultProviderId: integer('default_provider_id'),
    showPoweredBy: boolean('show_powered_by').default(true),
    faviconUrl: varchar('favicon_url', { length: 255 }),
    type: varchar({ length: 255 })
  },
  (table) => [
    foreignKey({
      columns: [table.parentId],
      foreignColumns: [table.id],
      name: 'tennant_master_parent_id_fkey'
    }),
    foreignKey({
      columns: [table.defaultProviderId],
      foreignColumns: [users.userId],
      name: 'tennant_master_default_provider_id_fkey'
    })
  ]
);

export const tennantMasterRelations = relations(
  tennantMaster,
  ({ one, many }) => ({
    followUpReminders: many(followUpReminder),
    consultOrderFiles: many(consultOrderFiles),
    prescriptionPreferences: many(prescriptionPreference),
    lifefileConfigurations: many(lifefileConfiguration),
    tennantMaster: one(tennantMaster, {
      fields: [tennantMaster.parentId],
      references: [tennantMaster.id],
      relationName: 'tennantMaster_parentId_tennantMaster_id'
    }),
    tennantMasters: many(tennantMaster, {
      relationName: 'tennantMaster_parentId_tennantMaster_id'
    }),
    user: one(users, {
      fields: [tennantMaster.defaultProviderId],
      references: [users.userId],
      relationName: 'tennantMaster_defaultProviderId_users_userId'
    }),
    users: many(users, {
      relationName: 'users_tennantId_tennantMaster_id'
    }),
    tennantConfigs: many(tennantConfig),
    telehealthServiceMasters: many(telehealthServiceMaster),
    tenantAuthProviders: many(tenantAuthProvider),
    serviceActionPreferences: many(serviceActionPreference),
    userFiles: many(userFiles),
    tenantFiles: many(tenantFiles),
    telehealthServiceCategories: many(telehealthServiceCategory)
  })
);
