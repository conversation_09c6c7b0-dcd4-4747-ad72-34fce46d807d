import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { prescriptionTransferRequest } from './prescription_transfer_request';

export const enumPrescriptionTransferMedicationsStatus = pgEnum(
  'enum_prescription_transfer_medications_status',
  ['pending', 'transferred']
);
export const prescriptionTransferMedications = pgTable(
  'prescription_transfer_medications',
  {
    medicationId: serial('medication_id').primaryKey().notNull(),
    requestId: integer('request_id'),
    name: text().notNull(),
    isFulfilled: boolean('is_fulfilled').default(false),
    hgRxId: integer('hg_rx_id'),
    status: enumPrescriptionTransferMedicationsStatus().default('pending'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.requestId],
      foreignColumns: [prescriptionTransferRequest.requestId],
      name: 'prescription_transfer_medications_request_id_fkey'
    })
  ]
);

export const prescriptionTransferMedicationsRelations = relations(
  prescriptionTransferMedications,
  ({ one }) => ({
    prescriptionTransferRequest: one(prescriptionTransferRequest, {
      fields: [prescriptionTransferMedications.requestId],
      references: [prescriptionTransferRequest.requestId]
    })
  })
);
