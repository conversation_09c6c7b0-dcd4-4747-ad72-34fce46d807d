import {
  text,
  pgTable,
  varchar,
  integer,
  timestamp
} from 'drizzle-orm/pg-core';

export const mmsRequestPayload = pgTable('mms_request_payload', {
  id: integer().primaryKey().notNull(),
  rxno: varchar({ length: 50 }).notNull(),
  pharmacySystem: varchar('pharmacy_system', { length: 50 }).notNull(),
  pharmacyToken: varchar('pharmacy_token', { length: 50 }),
  payload: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
