import { relations } from 'drizzle-orm';
import {
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { tennantMaster } from './tennant_master';
import { users } from './users';
export const enumUserFilesType = pgEnum('enum_user_files_type', [
  'audio',
  'video',
  'file',
  'image'
]);
export const enumUserFileRepoDetailsUploadType = pgEnum(
  'enum_user_file_repo_details_upload_type',
  ['FAX', 'SFTP']
);
export const userFiles = pgTable(
  'user_files',
  {
    userFileId: serial('user_file_id').primaryKey().notNull(),
    userId: integer('user_id'),
    createdBy: integer('created_by'),
    doctorId: integer('doctor_id'),
    name: varchar({ length: 255 }),
    path: varchar({ length: 255 }),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    type: enumUserFilesType(),
    fileKey: varchar('file_key', { length: 255 }),
    tenantId: integer('tenant_id')
  },
  (table) => [
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'user_files_created_by_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_files_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_files_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_files_user_id_fkey'
    }),
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'user_files_tenant_id_fkey'
    })
  ]
);

export const userFilesRelations = relations(userFiles, ({ one }) => ({
  user_createdBy: one(users, {
    fields: [userFiles.createdBy],
    references: [users.userId],
    relationName: 'userFiles_createdBy_users_userId'
  }),
  user_doctorId: one(users, {
    fields: [userFiles.doctorId],
    references: [users.userId],
    relationName: 'userFiles_doctorId_users_userId'
  }),
  order: one(orders, {
    fields: [userFiles.orderId],
    references: [orders.id]
  }),
  user_userId: one(users, {
    fields: [userFiles.userId],
    references: [users.userId],
    relationName: 'userFiles_userId_users_userId'
  }),
  tennantMaster: one(tennantMaster, {
    fields: [userFiles.tenantId],
    references: [tennantMaster.id]
  })
}));
