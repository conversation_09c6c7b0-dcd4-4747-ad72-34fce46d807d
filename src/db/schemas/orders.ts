import { sql, relations } from 'drizzle-orm';
import {
  text,
  serial,
  unique,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { consultNotes } from './consult_notes';
import { providerRatings } from './provider_ratings';
import { referrals } from './referrals';
import { reviews } from './reviews';
import { schedules } from './schedules';
import { telehealthServiceOrder } from './telehealth_service_order';
import { transcriptions } from './transcriptions';
import { userDiet } from './user_diet';
import { userFiles } from './user_files';
import { userForms } from './user_forms';
import { userVitals } from './user_vitals';
import { users } from './users';
import { visitSummaryUploadStatus } from './visit_summary_upload_status';

export const enumOrdersCategory = pgEnum('enum_orders_category', [
  'CCM',
  'RPM',
  'BHI'
]);
export const enumOrdersStatus = pgEnum('enum_orders_status', [
  'STARTED',
  'INPROGRESS',
  'ENDED',
  'NOANSWER',
  'REJECTED',
  'QUEUED',
  'RINGING',
  'CANCELLED',
  'COMPLETED',
  'BUSY',
  'FAILED'
]);
export const enumOrdersType = pgEnum('enum_orders_type', [
  'audio',
  'video',
  'one-way',
  'online',
  'voip'
]);

export const orders = pgTable(
  'orders',
  {
    id: serial().primaryKey().notNull(),
    orderId: text('order_id').notNull(),
    scheduleId: integer('schedule_id'),
    startTime: timestamp('start_time', { withTimezone: true, mode: 'string' }),
    endTime: timestamp('end_time', { withTimezone: true, mode: 'string' }),
    callerId: integer('caller_id'),
    calleeId: integer('callee_id'),
    doctorId: integer('doctor_id'),
    status: enumOrdersStatus(),
    type: enumOrdersType(),
    category: enumOrdersCategory(),
    conversationMode: text('conversation_mode'),
    cost: numeric(),
    billed: boolean().default(false),
    callerLocation: varchar('caller_location', { length: 255 }),
    calleeLocation: varchar('callee_location', { length: 255 }),
    instructions: text(),
    diagnosis: text(),
    procedure: text(),
    visitSummary: text('visit_summary'),
    regenerateVisitSummary: boolean('regenerate_visit_summary').default(false),
    regenerateCcdFile: boolean('regenerate_ccd_file').default(false),
    isVirtualRoom: boolean('is_virtual_room').default(false),
    hideVisitDetails: boolean('hide_visit_details').default(false),
    audioStreamScreenshot: text('audio_stream_screenshot'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    currency: text(),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    duration: varchar({ length: 50 }),
    hostPassPhrase: varchar('host_pass_phrase', { length: 255 }),
    viewerPassPhrase: varchar('viewer_pass_phrase', { length: 255 }),
    channel: varchar({ length: 255 })
  },
  (table) => [
    foreignKey({
      columns: [table.calleeId],
      foreignColumns: [users.userId],
      name: 'orders_callee_id_fkey'
    }),
    foreignKey({
      columns: [table.callerId],
      foreignColumns: [users.userId],
      name: 'orders_caller_id_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'orders_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'orders_order_guid_fkey'
    }),
    foreignKey({
      columns: [table.scheduleId],
      foreignColumns: [schedules.scheduleId],
      name: 'orders_schedule_id_fkey'
    }),
    unique('orders_order_id_key').on(table.orderId)
  ]
);

export const ordersRelations = relations(orders, ({ one, many }) => ({
  consultNotes: many(consultNotes),
  telehealthServiceOrders: many(telehealthServiceOrder, {
    relationName: 'telehealthServiceOrder_orderId_orders_id'
  }),
  user_calleeId: one(users, {
    fields: [orders.calleeId],
    references: [users.userId],
    relationName: 'orders_calleeId_users_userId'
  }),
  user_callerId: one(users, {
    fields: [orders.callerId],
    references: [users.userId],
    relationName: 'orders_callerId_users_userId'
  }),
  user_doctorId: one(users, {
    fields: [orders.doctorId],
    references: [users.userId],
    relationName: 'orders_doctorId_users_userId'
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [orders.orderGuid],
    references: [telehealthServiceOrder.orderGuid],
    relationName: 'orders_orderGuid_telehealthServiceOrder_orderGuid'
  }),
  schedule: one(schedules, {
    fields: [orders.scheduleId],
    references: [schedules.scheduleId]
  }),
  visitSummaryUploadStatuses: many(visitSummaryUploadStatus),
  userForms: many(userForms),
  userDiets: many(userDiet),
  reviews: many(reviews),
  referrals: many(referrals),
  transcriptions: many(transcriptions),
  userVitals: many(userVitals),
  providerRatings: many(providerRatings),
  userFiles: many(userFiles)
}));
