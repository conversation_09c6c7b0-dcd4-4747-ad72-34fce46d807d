import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { tennantMaster } from './tennant_master';

export const enumConsultOrderFilesType = pgEnum(
  'enum_consult_order_files_type',
  ['audio', 'video', 'file', 'image']
);

export const consultOrderFiles = pgTable(
  'consult_order_files',
  {
    fileId: serial('file_id').primaryKey().notNull(),
    orderId: integer('order_id'),
    type: enumConsultOrderFilesType(),
    filePath: text('file_path').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    tennantId: integer('tennant_id'),
    fileKey: varchar('file_key', { length: 255 })
      .default('consult_file')
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_order_files_order_id_fkey'
    }),
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'consult_order_files_tennant_id_fkey'
    })
  ]
);

export const consultOrderFilesRelations = relations(
  consultOrderFiles,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [consultOrderFiles.orderId],
      references: [telehealthServiceOrder.id]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [consultOrderFiles.tennantId],
      references: [tennantMaster.id]
    })
  })
);
