import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { telehealthServiceProcedureCodesMapping } from './telehealth_service_procedure_codes_mapping';

export const procedureCodes = pgTable('procedure_codes', {
  procedureCodeId: serial('procedure_code_id').primaryKey().notNull(),
  code: varchar({ length: 255 }),
  description: varchar({ length: 500 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean(),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
});

export const procedureCodesRelations = relations(
  procedureCodes,
  ({ many }) => ({
    telehealthServiceProcedureCodesMappings: many(
      telehealthServiceProcedureCodesMapping
    )
  })
);
