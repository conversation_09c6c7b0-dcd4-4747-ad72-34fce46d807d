import { text, serial, pgTable, timestamp } from 'drizzle-orm/pg-core';

export const healthSummaryMetadata = pgTable('health_summary_metadata', {
  metadataId: serial('metadata_id').primaryKey().notNull(),
  category: text().notNull(),
  responseType: text('response_type'),
  template: text(),
  arrayFields: text('array_fields'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
