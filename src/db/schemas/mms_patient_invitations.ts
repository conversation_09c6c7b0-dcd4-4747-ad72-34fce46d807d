import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { mmsPatients } from './mms_patients';

export const mmsPatientInvitations = pgTable(
  'mms_patient_invitations',
  {
    invitationId: serial('invitation_id').primaryKey().notNull(),
    email: text(),
    phone: text(),
    code: text().notNull(),
    mmsPatientId: integer('mms_patient_id').notNull(),
    accepted: boolean().default(false),
    expiry: timestamp({ withTimezone: true, mode: 'string' }),
    remainingTries: integer('remaining_tries').default(3),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.mmsPatientId],
      foreignColumns: [mmsPatients.patientId],
      name: 'mms_patient_invitations_mms_patient_id_fkey'
    })
  ]
);

export const mmsPatientInvitationsRelations = relations(
  mmsPatientInvitations,
  ({ one }) => ({
    mmsPatient: one(mmsPatients, {
      fields: [mmsPatientInvitations.mmsPatientId],
      references: [mmsPatients.patientId]
    })
  })
);
