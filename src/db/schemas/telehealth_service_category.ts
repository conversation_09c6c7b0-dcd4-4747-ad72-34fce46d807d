import { sql, relations } from 'drizzle-orm';
import {
  uuid,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { tennantMaster } from './tennant_master';

export const telehealthServiceCategory = pgTable(
  'telehealth_service_category',
  {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 200 }).notNull(),
    slug: varchar({ length: 200 }).notNull(),
    title: varchar({ length: 200 }),
    subtitle: varchar({ length: 500 }),
    icon: varchar({ length: 500 }).notNull(),
    filterDisplay: boolean('filter_display').default(true).notNull(),
    serviceCategoryGuid: uuid('service_category_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    tennantId: integer('tennant_id'),
    createdAt: timestamp('created_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    updatedAt: timestamp('updated_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'telehealth_service_category_tennant_id_fkey'
    })
  ]
);

export const telehealthServiceCategoryRelations = relations(
  telehealthServiceCategory,
  ({ one }) => ({
    tennantMaster: one(tennantMaster, {
      fields: [telehealthServiceCategory.tennantId],
      references: [tennantMaster.id]
    })
  })
);
