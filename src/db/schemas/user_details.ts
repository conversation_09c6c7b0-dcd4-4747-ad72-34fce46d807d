import { relations } from 'drizzle-orm';
import {
  text,
  json,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userDetails = pgTable(
  'user_details',
  {
    userDetailId: serial('user_detail_id').primaryKey().notNull(),
    userId: integer('user_id'),
    data: text(),
    signature: varchar({ length: 255 }),
    specialty: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    address: text(),
    deleted: boolean().default(false),
    bio: text(),
    language: json().default([])
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_details_user_id_fkey'
    })
  ]
);

export const userDetailsRelations = relations(userDetails, ({ one }) => ({
  user: one(users, {
    fields: [userDetails.userId],
    references: [users.userId]
  })
}));
