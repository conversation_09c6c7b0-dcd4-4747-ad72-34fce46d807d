import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  integer,
  boolean,
  foreignKey
} from 'drizzle-orm/pg-core';

import { medicines } from './medicines';
import { pharmacies } from './pharmacies';
import { telehealthServices } from './telehealth_services';

export const medicineServicePharmacyMapping = pgTable(
  'medicine_service_pharmacy_mapping',
  {
    id: serial().primaryKey().notNull(),
    medicineId: integer('medicine_id').notNull(),
    pharmacyId: integer('pharmacy_id').notNull(),
    serviceId: integer('service_id').notNull(),
    status: boolean().default(true)
  },
  (table) => [
    foreignKey({
      columns: [table.medicineId],
      foreignColumns: [medicines.medicineId],
      name: 'medicine_service_pharmacy_mapping_medicine_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'medicine_service_pharmacy_mapping_pharmacy_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'medicine_service_pharmacy_mapping_service_id_fkey'
    })
  ]
);

export const medicineServicePharmacyMappingRelations = relations(
  medicineServicePharmacyMapping,
  ({ one }) => ({
    medicine: one(medicines, {
      fields: [medicineServicePharmacyMapping.medicineId],
      references: [medicines.medicineId]
    }),
    pharmacy: one(pharmacies, {
      fields: [medicineServicePharmacyMapping.pharmacyId],
      references: [pharmacies.pharmacyId]
    }),
    telehealthService: one(telehealthServices, {
      fields: [medicineServicePharmacyMapping.serviceId],
      references: [telehealthServices.id]
    })
  })
);
