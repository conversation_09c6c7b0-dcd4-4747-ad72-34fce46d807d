import { sql, relations } from 'drizzle-orm';
import {
  text,
  jsonb,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const consultNoteTemplates = pgTable(
  'consult_note_templates',
  {
    id: serial().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    title: varchar({ length: 150 }).default(''),
    icdCode: jsonb('icd_code').default([]),
    cptCode: jsonb('cpt_code').default([]),
    subjective: text().default(''),
    objective: text().default(''),
    assessment: text().default(''),
    plan: text().default(''),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'consult_note_templates_user_id_fkey'
    })
  ]
);

export const consultNoteTemplatesRelations = relations(
  consultNoteTemplates,
  ({ one }) => ({
    user: one(users, {
      fields: [consultNoteTemplates.userId],
      references: [users.userId]
    })
  })
);
