import { sql, relations } from 'drizzle-orm';
import {
  text,
  uuid,
  serial,
  unique,
  pgTable,
  varchar,
  boolean,
  timestamp
} from 'drizzle-orm/pg-core';

import { lifefileConfiguration } from './lifefile_configuration';
import { medicineServicePharmacyMapping } from './medicine_service_pharmacy_mapping';
import { mmsPatients } from './mms_patients';
import { pharmacyStateServiceMapping } from './pharmacy_state_service_mapping';
import { prescriptionPreference } from './prescription_preference';
import { telehealthServiceOrder } from './telehealth_service_order';

export const pharmacies = pgTable(
  'pharmacies',
  {
    pharmacyId: serial('pharmacy_id').primaryKey().notNull(),
    npi: varchar({ length: 255 }),
    name: varchar({ length: 255 }),
    nabp: varchar({ length: 255 }),
    address: text(),
    phone: varchar({ length: 255 }),
    fax: varchar({ length: 255 }),
    email: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    ncpdpid: varchar({ length: 50 }),
    pharmcistName: varchar('pharmcist_name', { length: 50 }),
    dea: varchar({ length: 50 }),
    pharmacyLegalName: varchar('pharmacy_legal_name', { length: 100 }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false).notNull(),
    pharmaciesGuid: uuid('pharmacies_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull()
  },
  (table) => [unique('pharmacies_npi_key').on(table.npi)]
);

export const pharmaciesRelations = relations(pharmacies, ({ many }) => ({
  mmsPatients: many(mmsPatients),
  telehealthServiceOrders: many(telehealthServiceOrder),
  medicineServicePharmacyMappings: many(medicineServicePharmacyMapping),
  prescriptionPreferences: many(prescriptionPreference),
  lifefileConfigurations: many(lifefileConfiguration),
  pharmacyStateServiceMappings: many(pharmacyStateServiceMapping)
}));
