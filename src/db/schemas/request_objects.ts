import { relations } from 'drizzle-orm';
import { serial, pgTable, varchar, timestamp } from 'drizzle-orm/pg-core';

import { requests } from './requests';

export const requestObjects = pgTable('request_objects', {
  objectId: serial('object_id').primaryKey().notNull(),
  name: varchar({ length: 255 }).notNull(),
  type: varchar({ length: 255 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const requestObjectsRelations = relations(
  requestObjects,
  ({ many }) => ({
    requests: many(requests)
  })
);
