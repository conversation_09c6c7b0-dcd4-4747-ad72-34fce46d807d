import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const invitations = pgTable(
  'invitations',
  {
    invitationId: serial('invitation_id').primaryKey().notNull(),
    invitorId: integer('invitor_id').notNull(),
    installType: varchar('install_type', { length: 255 }),
    email: varchar({ length: 255 }),
    phone: varchar({ length: 255 }),
    accepted: boolean().default(false),
    addPracticeGroupDoctors: boolean('add_practice_group_doctors').default(
      false
    ),
    code: varchar({ length: 255 }),
    expiry: timestamp({ withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.invitorId],
      foreignColumns: [users.userId],
      name: 'invitations_invitor_id_fkey'
    })
  ]
);

export const invitationsRelations = relations(invitations, ({ one }) => ({
  user: one(users, {
    fields: [invitations.invitorId],
    references: [users.userId]
  })
}));
