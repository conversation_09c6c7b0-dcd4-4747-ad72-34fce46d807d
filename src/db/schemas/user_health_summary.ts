import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { allergiesValues } from './allergies_values';
import { carePlanValues } from './care_plan_values';
import { diagnosesValues } from './diagnoses_values';
import { documentValues } from './document_values';
import { encountersValues } from './encounters_values';
import { familyHistoryValues } from './family_history_values';
import { immunizationsValues } from './immunizations_values';
import { medicationsValues } from './medications_values';
import { proceduresValues } from './procedures_values';
import { resultsValues } from './results_values';
import { socialHistoryValues } from './social_history_values';
import { users } from './users';

export const userHealthSummary = pgTable(
  'user_health_summary',
  {
    summaryId: serial('summary_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    type: varchar({ length: 255 }),
    data: text(),
    sourcePlatform: varchar('source_platform', { length: 255 }),
    hasDetail: boolean('has_detail').default(false),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_health_summary_user_id_fkey'
    })
  ]
);

export const userHealthSummaryRelations = relations(
  userHealthSummary,
  ({ one, many }) => ({
    encountersValues: many(encountersValues),
    immunizationsValues: many(immunizationsValues),
    medicationsValues: many(medicationsValues),
    socialHistoryValues: many(socialHistoryValues),
    user: one(users, {
      fields: [userHealthSummary.userId],
      references: [users.userId]
    }),
    diagnosesValues: many(diagnosesValues),
    familyHistoryValues: many(familyHistoryValues),
    proceduresValues: many(proceduresValues),
    carePlanValues: many(carePlanValues),
    resultsValues: many(resultsValues),
    allergiesValues: many(allergiesValues),
    documentValues: many(documentValues)
  })
);
