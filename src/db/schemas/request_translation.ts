import { relations } from 'drizzle-orm';
import {
  text,
  pgTable,
  varchar,
  integer,
  foreignKey,
  primaryKey
} from 'drizzle-orm/pg-core';

import { requests } from './requests';

export const requestTranslation = pgTable(
  'request_translation',
  {
    requestId: integer('request_id').notNull(),
    languageCode: varchar('language_code', { length: 255 }).notNull(),
    message: text(),
    patientHistory: text('patient_history'),
    detail: text()
  },
  (table) => [
    foreignKey({
      columns: [table.requestId],
      foreignColumns: [requests.requestId],
      name: 'request_translation_request_id_fkey'
    }),
    primaryKey({
      columns: [table.requestId, table.languageCode],
      name: 'request_translation_pkey'
    })
  ]
);

export const requestTranslationRelations = relations(
  requestTranslation,
  ({ one }) => ({
    request: one(requests, {
      fields: [requestTranslation.requestId],
      references: [requests.requestId]
    })
  })
);
