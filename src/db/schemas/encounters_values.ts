import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { userHealthSummary } from './user_health_summary';

export const encountersValues = pgTable(
  'encounters_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'encounters_values_summary_id_fkey'
    })
  ]
);

export const encountersValuesRelations = relations(
  encountersValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [encountersValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);
