import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userParticlehealth = pgTable(
  'user_particlehealth',
  {
    healthId: serial('health_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    queryId: varchar('query_id', { length: 255 }),
    status: varchar({ length: 255 }),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    buserId: integer('buser_id').notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_particlehealth_user_id_fkey'
    }),
    foreignKey({
      columns: [table.buserId],
      foreignColumns: [users.userId],
      name: 'user_particlehealth_buser_id_fkey'
    })
  ]
);

export const userParticlehealthRelations = relations(
  userParticlehealth,
  ({ one }) => ({
    user_userId: one(users, {
      fields: [userParticlehealth.userId],
      references: [users.userId],
      relationName: 'userParticlehealth_userId_users_userId'
    }),
    user_buserId: one(users, {
      fields: [userParticlehealth.buserId],
      references: [users.userId],
      relationName: 'userParticlehealth_buserId_users_userId'
    })
  })
);
