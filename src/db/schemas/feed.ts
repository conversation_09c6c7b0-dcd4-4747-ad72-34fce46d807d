import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const feed = pgTable(
  'feed',
  {
    feedId: serial('feed_id').primaryKey().notNull(),
    description: text(),
    address: text(),
    imageUrl: text('image_url'),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    metadata: text(),
    userId: integer('user_id'),
    createdBy: integer('created_by'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'feed_created_by_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'feed_user_id_fkey'
    })
  ]
);

export const feedRelations = relations(feed, ({ one }) => ({
  user_createdBy: one(users, {
    fields: [feed.createdBy],
    references: [users.userId],
    relationName: 'feed_createdBy_users_userId'
  }),
  user_userId: one(users, {
    fields: [feed.userId],
    references: [users.userId],
    relationName: 'feed_userId_users_userId'
  })
}));
