import { relations } from 'drizzle-orm';
import {
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';
import { telehealthServiceQuestions } from './telehealth_service_questions';

export const telehealthServiceQuestionAnswer = pgTable(
  'telehealth_service_question_answer',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id'),
    questionId: integer('question_id'),
    answer: varchar({ length: 2000 }),
    otherText: varchar('other_text', { length: 2000 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.questionId],
      foreignColumns: [telehealthServiceQuestions.id],
      name: 'telehealth_service_question_answer_question_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'telehealth_service_question_answer_service_order_id_fkey'
    })
  ]
);

export const telehealthServiceQuestionAnswerRelations = relations(
  telehealthServiceQuestionAnswer,
  ({ one }) => ({
    telehealthServiceQuestion: one(telehealthServiceQuestions, {
      fields: [telehealthServiceQuestionAnswer.questionId],
      references: [telehealthServiceQuestions.id]
    }),
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [telehealthServiceQuestionAnswer.serviceOrderId],
      references: [telehealthServiceOrder.id]
    })
  })
);
