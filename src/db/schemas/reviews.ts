import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { orders } from './orders';
import { users } from './users';

export const reviews = pgTable(
  'reviews',
  {
    reviewId: serial('review_id').primaryKey().notNull(),
    orderId: text('order_id'),
    review: text().notNull(),
    givenBy: integer('given_by'),
    givenTo: integer('given_to'),
    rating: integer(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    callQuality: integer('call_quality').default(0),
    isRecommendProvider: boolean('is_recommend_provider'),
    consultationRating: integer('consultation_rating').default(0),
    userComment: text('user_comment').default('')
  },
  (table) => [
    foreignKey({
      columns: [table.givenBy],
      foreignColumns: [users.userId],
      name: 'reviews_given_by_fkey'
    }),
    foreignKey({
      columns: [table.givenTo],
      foreignColumns: [users.userId],
      name: 'reviews_given_to_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.orderId],
      name: 'reviews_order_id_fkey'
    })
  ]
);

export const reviewsRelations = relations(reviews, ({ one }) => ({
  user_givenBy: one(users, {
    fields: [reviews.givenBy],
    references: [users.userId],
    relationName: 'reviews_givenBy_users_userId'
  }),
  user_givenTo: one(users, {
    fields: [reviews.givenTo],
    references: [users.userId],
    relationName: 'reviews_givenTo_users_userId'
  }),
  order: one(orders, {
    fields: [reviews.orderId],
    references: [orders.orderId]
  })
}));
