import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const encryptionKeys = pgTable(
  'encryption_keys',
  {
    encryptionKeyId: serial('encryption_key_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    deviceId: integer('device_id').default(1).notNull(),
    registrationId: integer('registration_id').notNull(),
    identityKey: text('identity_key'),
    signedPreKey: text('signed_pre_key'),
    preKey: text('pre_key'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'encryption_keys_user_id_fkey'
    })
  ]
);

export const encryptionKeysRelations = relations(encryptionKeys, ({ one }) => ({
  user: one(users, {
    fields: [encryptionKeys.userId],
    references: [users.userId]
  })
}));
