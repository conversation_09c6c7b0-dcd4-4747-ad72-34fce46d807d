import { sql, relations } from 'drizzle-orm';
import {
  text,
  serial,
  unique,
  pgEnum,
  pgTable,
  varchar,
  integer,
  boolean,
  numeric,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { authProvider } from './auth_provider';
import { chatFiles } from './chat_files';
import { chatMessages } from './chat_messages';
import { chatRoomMembers } from './chat_room_members';
import { consultNoteTemplates } from './consult_note_templates';
import { consultReassignHistory } from './consult_reassign_history';
import { consultUpdateDetailsHistory } from './consult_update_details_history';
import { conversationMessages } from './conversation_messages';
import { conversations } from './conversations';
import { educationalVideos } from './educational_videos';
import { encryptionKeys } from './encryption_keys';
import { externalRequestsLog } from './external_requests_log';
import { favouriteDrugs } from './favourite_drugs';
import { faxes } from './faxes';
import { feed } from './feed';
import { followUpReminder } from './follow_up_reminder';
import { healthSummariesLog } from './health_summaries_log';
import { healthSummariesSchedule } from './health_summaries_schedule';
import { invitations } from './invitations';
import { loginRequests } from './login_requests';
import { mmsPatients } from './mms_patients';
import { notificationReminder } from './notification_reminder';
import { onehealthLabOrders } from './onehealth_lab_orders';
import { orders } from './orders';
import { permissionGroups } from './permission_groups';
import { prescriptionTransferRequest } from './prescription_transfer_request';
import { providerLicense } from './provider_license';
import { providerRatings } from './provider_ratings';
import { referrals } from './referrals';
import { requests } from './requests';
import { requestsLog } from './requests_log';
import { reviews } from './reviews';
import { schedules } from './schedules';
import { stripeUserDetails } from './stripe_user_details';
import { stripeUserPaymentDetails } from './stripe_user_payment_details';
import { subscriptionPlans } from './subscription_plans';
import { supportNotes } from './support_notes';
import { telehealthServiceOrder } from './telehealth_service_order';
import { telehealthServiceProviderMapping } from './telehealth_service_provider_mapping';
import { telehealthServiceQuestionAnswerDump } from './telehealth_service_question_answer_dump';
import { tennantMaster } from './tennant_master';
import { transactions } from './transactions';
import { transcriptions } from './transcriptions';
import { userAssociations } from './user_associations';
import { userDetails } from './user_details';
import { userDiet } from './user_diet';
import { userEducationalVideos } from './user_educational_videos';
import { userExternalIdMapping } from './user_external_id_mapping';
import { userFileRepoDetails } from './user_file_repo_details';
import { userFiles } from './user_files';
import { userForms } from './user_forms';
import { userHealthSummary } from './user_health_summary';
import { userIdentities } from './user_identities';
import { userInsurance } from './user_insurance';
import { userLicenses } from './user_licenses';
import { userParticlehealth } from './user_particlehealth';
import { userPracticeGroups } from './user_practice_groups';
import { userSchedules } from './user_schedules';
import { userSubscription } from './user_subscription';
import { userSubscriptionBilling } from './user_subscription_billing';
import { userViewers } from './user_viewers';
import { userVitals } from './user_vitals';
import { userVitalsDocuments } from './user_vitals_documents';
import { usersTranslation } from './users_translation';
import { visitSummaryUploadStatus } from './visit_summary_upload_status';
import { vitalsNotificationCycle } from './vitals_notification_cycle';
import { vitalsPatientMonitoring } from './vitals_patient_monitoring';
import { vitalsSummaryUploadStatus } from './vitals_summary_upload_status';
export const enumUsersGender = pgEnum('enum_users_gender', [
  'male',
  'female',
  'others',
  'transgender-female',
  'transgender-male'
]);
export const enumUsersRole = pgEnum('enum_users_role', [
  'USER',
  'BUSER',
  'AUSER',
  'medical_assistant',
  'viewer',
  'support_user',
  'pharmacist',
  'PHARMACY',
  'GROUP_ADMIN',
  'SUPPORT_ADMIN',
  'DEVELOPER'
]);
export const enumUsersStatus = pgEnum('enum_users_status', [
  'AVAILABLE',
  'BUSY',
  'AWAY',
  'OFFLINE',
  'ACTIVATION_PENDING',
  'ONBOARDING_PENDING',
  'PROFILE_INCOMPLETE'
]);
export const enumUsersSubRole = pgEnum('enum_users_sub_role', [
  'USER',
  'DIETICIAN_NUTRITION',
  'MENTAL_HEALTH',
  'WEIGHT_LOSS_MANAGEMENT',
  'DIABETES_PREVENTION'
]);
export const users = pgTable(
  'users',
  {
    userId: serial('user_id').primaryKey().notNull(),
    userGuid: varchar('user_guid', { length: 255 }),
    password: text(),
    role: enumUsersRole().default('USER'),
    status: enumUsersStatus().default('OFFLINE'),
    installType: varchar('install_type', { length: 255 }),
    firstName: text('first_name'),
    lastName: text('last_name'),
    zipCode: varchar('zip_code', { length: 255 }),
    email: text().notNull(),
    phone: text(),
    otp: text(),
    dob: text(),
    apptLength: integer('appt_length'),
    apptStartTime: varchar('appt_start_time', { length: 255 }),
    apptEndTime: varchar('appt_end_time', { length: 255 }),
    secureMessage: boolean('secure_message').default(false),
    connectionRequests: boolean('connection_requests').default(false),
    vitalsCcdEnabled: boolean('vitals_ccd_enabled').default(false),
    apptRequests: boolean('appt_requests').default(false),
    trialValidity: timestamp('trial_validity', {
      withTimezone: true,
      mode: 'string'
    }),
    isCcCaptured: boolean('is_cc_captured'),
    gender: enumUsersGender().default('male'),
    deleted: boolean().default(false),
    emailVerified: boolean('email_verified').default(false),
    userAvatar: text('user_avatar'),
    idCard: text('id_card'),
    history: text(),
    questionnaire: text(),
    tokenValidity: integer('token_validity'),
    emailVerificationDetails: text('email_verification_details'),
    lastActive: timestamp('last_active', {
      withTimezone: true,
      mode: 'string'
    }),
    locked: timestamp({ withTimezone: true, mode: 'string' }),
    registrationKey: text('registration_key'),
    isRpmEnabled: boolean('is_rpm_enabled').default(false),
    isNotifyOnCapture: boolean('is_notify_on_capture').default(false),
    vitalsThresholds: text('vitals_thresholds'),
    vitalsCron: text('vitals_cron'),
    appDetails: text('app_details'),
    historyUpdatedAt: timestamp('history_updated_at', {
      withTimezone: true,
      mode: 'string'
    }),
    questionnaireUpdatedAt: timestamp('questionnaire_updated_at', {
      withTimezone: true,
      mode: 'string'
    }),
    debug: boolean().default(false),
    invitationCodeValidity: integer('invitation_code_validity'),
    cronExpression: text('cron_expression'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    mirthCcdEnabled: boolean('mirth_ccd_enabled').default(false),
    ccPaymentAccepted: boolean('cc_payment_accepted').default(false),
    recordingEnabled: boolean('recording_enabled').default(true),
    transcriptionEnabled: boolean('transcription_enabled').default(true),
    ordersEnabled: boolean('orders_enabled').default(true),
    showHealthSummaries: boolean('show_health_summaries'),
    healthgorillaId: varchar('healthgorilla_id', { length: 255 }),
    releaseMedical: boolean('release_medical'),
    telehealthServiceCost: numeric('telehealth_service_cost').default('0'),
    subRole: enumUsersSubRole('sub_role').default('USER'),
    myInviteCode: varchar('my_invite_code', { length: 15 }).default(sql`NULL`),
    referredByInviteCode: varchar('referred_by_invite_code', {
      length: 15
    }).default(sql`NULL`),
    referredByUserId: integer('referred_by_user_id'),
    email2: text('email_2'),
    tennantId: integer('tennant_id'),
    secondaryPhone: text('secondary_phone'),
    isTennantOwner: boolean('is_tennant_owner').default(false).notNull(),
    sendEmailCampaign: boolean('send_email_campaign').default(false).notNull(),
    appTimezone: varchar('app_timezone', { length: 255 }).default('{}'),
    rxPersonId: varchar({ length: 50 }),
    rxStatus: boolean().default(false),
    tenantAccess: varchar('tenant_access', { length: 500 }),
    dosespotApiResponse: varchar('dosespot_api_response', { length: 2000 }),
    idCardFile: text('id_card_file'),
    pharmacyAccess: varchar('pharmacy_access', { length: 500 }),
    parentId: integer('parent_id'),
    dependentAccountRelation: varchar('dependent_account_relation', {
      length: 255
    }),
    trackingCode: varchar('tracking_code', { length: 255 }),
    userPrivateKey: text('user_private_key')
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'users_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.parentId],
      foreignColumns: [table.userId],
      name: 'users_parent_id_fkey'
    }),
    unique('users_my_invite_code_key').on(table.myInviteCode)
  ]
);

export const usersRelations = relations(users, ({ one, many }) => ({
  authProviders: many(authProvider),
  educationalVideos: many(educationalVideos),
  requests_requesteeId: many(requests, {
    relationName: 'requests_requesteeId_users_userId'
  }),
  requests_requestorId: many(requests, {
    relationName: 'requests_requestorId_users_userId'
  }),
  encryptionKeys: many(encryptionKeys),
  healthSummariesSchedules: many(healthSummariesSchedule),
  feeds_createdBy: many(feed, {
    relationName: 'feed_createdBy_users_userId'
  }),
  feeds_userId: many(feed, {
    relationName: 'feed_userId_users_userId'
  }),
  invitations: many(invitations),
  loginRequests: many(loginRequests),
  favouriteDrugs: many(favouriteDrugs),
  mmsPatients: many(mmsPatients),
  onehealthLabOrders: many(onehealthLabOrders),
  permissionGroups_associatedUserId: many(permissionGroups, {
    relationName: 'permissionGroups_associatedUserId_users_userId'
  }),
  permissionGroups_patientId: many(permissionGroups, {
    relationName: 'permissionGroups_patientId_users_userId'
  }),
  telehealthServiceOrders_answerGivenBy: many(telehealthServiceOrder, {
    relationName: 'telehealthServiceOrder_answerGivenBy_users_userId'
  }),
  telehealthServiceOrders_providerId: many(telehealthServiceOrder, {
    relationName: 'telehealthServiceOrder_providerId_users_userId'
  }),
  orders_calleeId: many(orders, {
    relationName: 'orders_calleeId_users_userId'
  }),
  orders_callerId: many(orders, {
    relationName: 'orders_callerId_users_userId'
  }),
  orders_doctorId: many(orders, {
    relationName: 'orders_doctorId_users_userId'
  }),
  consultUpdateDetailsHistories: many(consultUpdateDetailsHistory),
  providerLicenses: many(providerLicense),
  requestsLogs: many(requestsLog),
  chatRoomMembers: many(chatRoomMembers),
  subscriptionPlans: many(subscriptionPlans),
  stripeUserPaymentDetails: many(stripeUserPaymentDetails),
  telehealthServiceProviderMappings: many(telehealthServiceProviderMapping),
  transactions_payeeUserId: many(transactions, {
    relationName: 'transactions_payeeUserId_users_userId'
  }),
  transactions_payerUserId: many(transactions, {
    relationName: 'transactions_payerUserId_users_userId'
  }),
  stripeUserDetails: many(stripeUserDetails),
  userParticlehealths_userId: many(userParticlehealth, {
    relationName: 'userParticlehealth_userId_users_userId'
  }),
  userParticlehealths_buserId: many(userParticlehealth, {
    relationName: 'userParticlehealth_buserId_users_userId'
  }),
  userDetails: many(userDetails),
  userEducationalVideos_referredBy: many(userEducationalVideos, {
    relationName: 'userEducationalVideos_referredBy_users_userId'
  }),
  userEducationalVideos_referredFor: many(userEducationalVideos, {
    relationName: 'userEducationalVideos_referredFor_users_userId'
  }),
  userEducationalVideos_doctorId: many(userEducationalVideos, {
    relationName: 'userEducationalVideos_doctorId_users_userId'
  }),
  userIdentities: many(userIdentities),
  followUpReminders: many(followUpReminder),
  userFileRepoDetails: many(userFileRepoDetails),
  userHealthSummaries: many(userHealthSummary),
  userSubscriptionBillings: many(userSubscriptionBilling),
  userSubscriptions: many(userSubscription),
  userVitalsDocuments_doctorId: many(userVitalsDocuments, {
    relationName: 'userVitalsDocuments_doctorId_users_userId'
  }),
  userVitalsDocuments_userId: many(userVitalsDocuments, {
    relationName: 'userVitalsDocuments_userId_users_userId'
  }),
  chatMessages: many(chatMessages),
  userSchedules_userId: many(userSchedules, {
    relationName: 'userSchedules_userId_users_userId'
  }),
  userSchedules_createdBy: many(userSchedules, {
    relationName: 'userSchedules_createdBy_users_userId'
  }),
  userSchedules_updatedBy: many(userSchedules, {
    relationName: 'userSchedules_updatedBy_users_userId'
  }),
  userSchedules_deletedBy: many(userSchedules, {
    relationName: 'userSchedules_deletedBy_users_userId'
  }),
  chatFiles: many(chatFiles),
  telehealthServiceQuestionAnswerDumps: many(
    telehealthServiceQuestionAnswerDump
  ),
  tennantMasters: many(tennantMaster, {
    relationName: 'tennantMaster_defaultProviderId_users_userId'
  }),
  consultReassignHistories_previousProvider: many(consultReassignHistory, {
    relationName: 'consultReassignHistory_previousProvider_users_userId'
  }),
  consultReassignHistories_updatedProvider: many(consultReassignHistory, {
    relationName: 'consultReassignHistory_updatedProvider_users_userId'
  }),
  consultReassignHistories_reassignedBy: many(consultReassignHistory, {
    relationName: 'consultReassignHistory_reassignedBy_users_userId'
  }),
  tennantMaster: one(tennantMaster, {
    fields: [users.tennantId],
    references: [tennantMaster.id],
    relationName: 'users_tennantId_tennantMaster_id'
  }),
  user: one(users, {
    fields: [users.parentId],
    references: [users.userId],
    relationName: 'users_parentId_users_userId'
  }),
  users: many(users, {
    relationName: 'users_parentId_users_userId'
  }),
  externalRequestsLogs: many(externalRequestsLog),
  supportNotes_userId: many(supportNotes, {
    relationName: 'supportNotes_userId_users_userId'
  }),
  supportNotes_supportUserId: many(supportNotes, {
    relationName: 'supportNotes_supportUserId_users_userId'
  }),
  vitalsSummaryUploadStatuses_doctorId: many(vitalsSummaryUploadStatus, {
    relationName: 'vitalsSummaryUploadStatus_doctorId_users_userId'
  }),
  vitalsSummaryUploadStatuses_patientId: many(vitalsSummaryUploadStatus, {
    relationName: 'vitalsSummaryUploadStatus_patientId_users_userId'
  }),
  vitalsNotificationCycles: many(vitalsNotificationCycle),
  visitSummaryUploadStatuses: many(visitSummaryUploadStatus),
  vitalsPatientMonitorings_buserId: many(vitalsPatientMonitoring, {
    relationName: 'vitalsPatientMonitoring_buserId_users_userId'
  }),
  vitalsPatientMonitorings_patientId: many(vitalsPatientMonitoring, {
    relationName: 'vitalsPatientMonitoring_patientId_users_userId'
  }),
  userForms_assignedBy: many(userForms, {
    relationName: 'userForms_assignedBy_users_userId'
  }),
  userForms_assignedTo: many(userForms, {
    relationName: 'userForms_assignedTo_users_userId'
  }),
  userForms_doctorId: many(userForms, {
    relationName: 'userForms_doctorId_users_userId'
  }),
  userDiets: many(userDiet),
  faxes_sentBy: many(faxes, {
    relationName: 'faxes_sentBy_users_userId'
  }),
  faxes_sentFor: many(faxes, {
    relationName: 'faxes_sentFor_users_userId'
  }),
  reviews_givenBy: many(reviews, {
    relationName: 'reviews_givenBy_users_userId'
  }),
  reviews_givenTo: many(reviews, {
    relationName: 'reviews_givenTo_users_userId'
  }),
  referrals_doctorId: many(referrals, {
    relationName: 'referrals_doctorId_users_userId'
  }),
  referrals_referredBy: many(referrals, {
    relationName: 'referrals_referredBy_users_userId'
  }),
  referrals_referredFor: many(referrals, {
    relationName: 'referrals_referredFor_users_userId'
  }),
  transcriptions: many(transcriptions),
  userVitals: many(userVitals),
  providerRatings_providerId: many(providerRatings, {
    relationName: 'providerRatings_providerId_users_userId'
  }),
  providerRatings_userId: many(providerRatings, {
    relationName: 'providerRatings_userId_users_userId'
  }),
  schedules_scheduledBy: many(schedules, {
    relationName: 'schedules_scheduledBy_users_userId'
  }),
  schedules_scheduledWith: many(schedules, {
    relationName: 'schedules_scheduledWith_users_userId'
  }),
  healthSummariesLogs: many(healthSummariesLog),
  conversationMessages: many(conversationMessages),
  conversations_userOne: many(conversations, {
    relationName: 'conversations_userOne_users_userId'
  }),
  conversations_userTwo: many(conversations, {
    relationName: 'conversations_userTwo_users_userId'
  }),
  prescriptionTransferRequests: many(prescriptionTransferRequest),
  userInsurances: many(userInsurance),
  userExternalIdMappings: many(userExternalIdMapping),
  userFiles_createdBy: many(userFiles, {
    relationName: 'userFiles_createdBy_users_userId'
  }),
  userFiles_doctorId: many(userFiles, {
    relationName: 'userFiles_doctorId_users_userId'
  }),
  userFiles_userId: many(userFiles, {
    relationName: 'userFiles_userId_users_userId'
  }),
  consultNoteTemplates: many(consultNoteTemplates),
  userLicenses: many(userLicenses),
  notificationReminders: many(notificationReminder),
  userPracticeGroups: many(userPracticeGroups),
  usersTranslations: many(usersTranslation),
  userViewers_userId: many(userViewers, {
    relationName: 'userViewers_userId_users_userId'
  }),
  userViewers_viewerId: many(userViewers, {
    relationName: 'userViewers_viewerId_users_userId'
  }),
  userAssociations_buserId: many(userAssociations, {
    relationName: 'userAssociations_buserId_users_userId'
  }),
  userAssociations_userId: many(userAssociations, {
    relationName: 'userAssociations_userId_users_userId'
  })
}));
