import { relations } from 'drizzle-orm';
import {
  pgTable,
  integer,
  boolean,
  timestamp,
  foreignKey,
  primaryKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userViewers = pgTable(
  'user_viewers',
  {
    userId: integer('user_id').notNull(),
    viewerId: integer('viewer_id').notNull(),
    userFavorite: boolean('user_favorite').default(false),
    viewerFavorite: boolean('viewer_favorite').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_viewers_user_id_fkey'
    }),
    foreignKey({
      columns: [table.viewerId],
      foreignColumns: [users.userId],
      name: 'user_viewers_viewer_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.viewerId],
      name: 'user_viewers_pkey'
    })
  ]
);

export const userViewersRelations = relations(userViewers, ({ one }) => ({
  user_userId: one(users, {
    fields: [userViewers.userId],
    references: [users.userId],
    relationName: 'userViewers_userId_users_userId'
  }),
  user_viewerId: one(users, {
    fields: [userViewers.viewerId],
    references: [users.userId],
    relationName: 'userViewers_viewerId_users_userId'
  })
}));
