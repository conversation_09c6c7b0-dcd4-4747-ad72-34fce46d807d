import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userVitalsDocuments = pgTable(
  'user_vitals_documents',
  {
    userVitalsDocumentId: serial('user_vitals_document_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id'),
    doctorId: integer('doctor_id'),
    path: text(),
    startTime: timestamp('start_time', { withTimezone: true, mode: 'string' }),
    endTime: timestamp('end_time', { withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_vitals_documents_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_vitals_documents_user_id_fkey'
    })
  ]
);

export const userVitalsDocumentsRelations = relations(
  userVitalsDocuments,
  ({ one }) => ({
    user_doctorId: one(users, {
      fields: [userVitalsDocuments.doctorId],
      references: [users.userId],
      relationName: 'userVitalsDocuments_doctorId_users_userId'
    }),
    user_userId: one(users, {
      fields: [userVitalsDocuments.userId],
      references: [users.userId],
      relationName: 'userVitalsDocuments_userId_users_userId'
    })
  })
);
