import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  boolean,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { telehealthServiceOrder } from './telehealth_service_order';

export const insuranceEligibilityLogs = pgTable(
  'insurance_eligibility_logs',
  {
    insuranceEligibilityLogId: serial('insurance_eligibility_log_id')
      .primaryKey()
      .notNull(),
    orderId: integer('order_id').notNull(),
    insuranceMemberId: varchar('insurance_member_id', {
      length: 255
    }).notNull(),
    fullResponse: text('full_response').notNull(),
    isEligible: boolean('is_eligible').notNull(),
    ineligibleReason: text('ineligible_reason'),
    benefitType: varchar('benefit_type', { length: 255 }),
    benefitAmount: varchar('benefit_amount', { length: 255 }),
    benefitPercentage: varchar('benefit_percentage', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'insurance_eligibility_logs_order_id_fkey'
    })
  ]
);

export const insuranceEligibilityLogsRelations = relations(
  insuranceEligibilityLogs,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [insuranceEligibilityLogs.orderId],
      references: [telehealthServiceOrder.id]
    })
  })
);
