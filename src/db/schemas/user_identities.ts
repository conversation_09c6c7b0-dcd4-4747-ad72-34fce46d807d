import { relations } from 'drizzle-orm';
import {
  text,
  serial,
  pgTable,
  varchar,
  integer,
  timestamp,
  foreignKey
} from 'drizzle-orm/pg-core';

import { users } from './users';

export const userIdentities = pgTable(
  'user_identities',
  {
    userIdentityId: serial('user_identity_id').primaryKey().notNull(),
    userId: integer('user_id'),
    identifier: text(),
    type: varchar({ length: 255 }),
    installType: varchar('install_type', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_identities_user_id_fkey'
    })
  ]
);

export const userIdentitiesRelations = relations(userIdentities, ({ one }) => ({
  user: one(users, {
    fields: [userIdentities.userId],
    references: [users.userId]
  })
}));
