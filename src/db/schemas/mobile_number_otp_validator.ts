import { uuid, pgTable, varchar, timestamp } from 'drizzle-orm/pg-core';

export const mobileNumberOtpValidator = pgTable('mobile_number_otp_validator', {
  id: uuid().primaryKey().notNull(),
  phone: varchar({ length: 20 }).notNull(),
  otp: varchar({ length: 1000 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
