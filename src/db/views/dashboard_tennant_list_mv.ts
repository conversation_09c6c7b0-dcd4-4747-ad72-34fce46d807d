import { sql } from 'drizzle-orm';
import {
  bigint,
  varchar,
  integer,
  pgMaterializedView
} from 'drizzle-orm/pg-core';

export const dashboardTennantListMv = pgMaterializedView(
  'dashboard_tennant_list_mv',
  {
    id: integer(),
    tennantName: varchar('tennant_name', { length: 255 }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    asyncConsultCount: bigint('async_consult_count', { mode: 'number' }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    syncConsultCount: bigint('sync_consult_count', { mode: 'number' }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    totalConsultCount: bigint('total_consult_count', { mode: 'number' })
  }
).as(
  sql`SELECT tennant_master.id, tennant_master.tennant_name, sum( CASE WHEN telehealth_service_order.service_type = 'ASYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS async_consult_count, sum( CASE WHEN telehealth_service_order.service_type = 'SYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS sync_consult_count, count(telehealth_service_order.id) AS total_consult_count FROM tennant_master JOIN users ON tennant_master.id = users.tennant_id JOIN telehealth_service_order ON users.user_id = telehealth_service_order.answer_given_by GROUP BY tennant_master.id, tennant_master.tennant_name ORDER BY tennant_master.id`
);
