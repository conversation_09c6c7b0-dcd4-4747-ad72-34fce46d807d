declare namespace Auth {
  // User types (matching the actual database schema)
  interface User {
    userId: number;
    userGuid: string | null;
    email: string;
    password: string | null;
    firstName: string | null;
    lastName: string | null;
    phone: string | null;
    role:
      | 'USER'
      | 'BUSER'
      | 'AUSER'
      | 'medical_assistant'
      | 'viewer'
      | 'support_user'
      | 'pharmacist'
      | 'PHARMACY'
      | 'GROUP_ADMIN'
      | 'SUPPORT_ADMIN'
      | 'DEVELOPER'
      | null;
    status:
      | 'AVAILABLE'
      | 'BUSY'
      | 'AWAY'
      | 'OFFLINE'
      | 'ACTIVATION_PENDING'
      | 'ONBOARDING_PENDING'
      | 'PROFILE_INCOMPLETE'
      | null;
    emailVerified: boolean | null;
    deleted: boolean | null;
    otp: string | null;
    createdAt: string | null;
    updatedAt: string | null;
  }

  // Auth DTOs
  interface LoginDTO {
    email: string;
    password: string;
  }

  interface RegisterDTO {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  }

  interface VerifyOtpDTO {
    email: string;
    otp: string;
  }

  interface ResetPasswordDTO {
    email: string;
    otp: string;
    newPassword: string;
  }

  // Auth responses
  interface AuthResponse {
    token: string;
    user: Omit<User, 'password'>;
  }

  interface RegisterResponse {
    message: string;
    userId: number;
    requiresVerification: boolean;
  }

  interface OtpResponse {
    message: string;
    expiresAt: Date;
  }

  // JWT payload
  interface JwtPayload {
    userId: number;
    email: string;
    role: string;
    iat?: number;
    exp?: number;
  }

  // Service interfaces
  interface AuthServiceInterface {
    login(dto: LoginDTO): Promise<AuthResponse>;
    register(dto: RegisterDTO): Promise<RegisterResponse>;
    verifyOtp(dto: VerifyOtpDTO): Promise<AuthResponse>;
    sendOtp(email: string): Promise<OtpResponse>;
    resetPassword(dto: ResetPasswordDTO): Promise<{ message: string }>;
    generateToken(user: User): string;
    verifyToken(token: string): JwtPayload;
    hashPassword(password: string): Promise<string>;
    verifyPassword(password: string, hashedPassword: string): Promise<boolean>;
  }
}

export = Auth;
export as namespace Auth;
