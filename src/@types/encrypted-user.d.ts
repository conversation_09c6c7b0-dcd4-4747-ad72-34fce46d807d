declare namespace EncryptedUser {
  interface CreateDTO {
    email: string;
    firstName: string;
    lastName: string;
  }

  interface UpdateDTO {
    email?: string;
    firstName?: string;
    lastName?: string;
  }

  interface DecryptedUser {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: Date | null;
    updatedAt: Date | null;
  }

  interface UserResponse {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    updatedAt: string;
  }
}

export default EncryptedUser;
