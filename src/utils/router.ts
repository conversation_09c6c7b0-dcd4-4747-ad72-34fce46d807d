/**
 * This module provides a utility function for creating Express routers with a cleaner syntax.
 * It encapsulates the router creation and configuration in a single function call.
 */

import { Router } from 'express';

/**
 * Creates and configures an Express router using a callback pattern.
 *
 * @param callback A function that receives the router instance and configures routes
 * @returns Configured Express Router instance
 *
 * @example
 * const apiRouter = createRouter(router => {
 *   router.get('/users', getUsersHandler);
 *   router.post('/users', createUserHandler);
 * });
 */
export function createRouter(callback: (router: Router) => void) {
  const router = Router();
  callback(router);
  return router;
}
