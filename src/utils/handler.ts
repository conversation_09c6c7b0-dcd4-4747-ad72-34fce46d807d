/**
 * This module provides type-safe request handler creation with built-in validation support.
 * It uses Zod for runtime validation while maintaining type safety through TypeScript.
 */

import { z } from 'zod';

import { validate, type RequestValidationSchema } from './validator';

import type { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Infers the type of Express Request object based on the validation schema.
 * It extracts types for params, body, and query from the schema if they exist.
 */
type InferRequest<T extends RequestValidationSchema> = Request<
  T['request'] extends { params: z.ZodType<any> }
    ? z.infer<T['request']['params']>
    : any,
  any,
  T['request'] extends { body: z.ZodType<any> }
    ? z.infer<T['request']['body']>
    : any,
  T['request'] extends { query: z.ZodType<any> }
    ? z.infer<T['request']['query']>
    : any
>;

/**
 * Infers the response body type from the validation schema.
 * If no response body schema is defined, defaults to 'any'.
 */
type InferResponse<T extends RequestValidationSchema> = T['response'] extends {
  body: z.ZodType<any>;
}
  ? z.infer<T['response']['body']>
  : any;

/**
 * Defines a type-safe request handler that works with the inferred request and response types.
 * This ensures compile-time type checking for request params, body, query, and response.
 */
type TypedHandler<T extends RequestValidationSchema> = (
  req: InferRequest<T>,
  res: Response<InferResponse<T>>,
  next: NextFunction
) => Promise<void>;

/**
 * Basic handler function type without validation schema.
 */
type HandlerFunction = (
  req: Request,
  res: Response,
  next: NextFunction
) => Promise<void>;

/**
 * Creates an Express request handler with optional validation.
 * @overload When provided with a schema and handler, performs validation before executing the handler
 */
export function createHandler<T extends RequestValidationSchema>(
  schema: T,
  handler: TypedHandler<T>
): RequestHandler;
/**
 * @overload When provided with just a handler, creates a basic request handler without validation
 */
export function createHandler(handler: HandlerFunction): RequestHandler;
export function createHandler<T extends RequestValidationSchema>(
  schemaOrHandler: T | HandlerFunction,
  handler?: TypedHandler<T>
): RequestHandler {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (typeof schemaOrHandler === 'function') {
        await schemaOrHandler(req, res, next);
      } else {
        // Validation will handle its own errors
        validate(schemaOrHandler)(req, res, async (error) => {
          if (error) {
            next(error);
            return;
          }
          try {
            await handler!(req, res, next);
          } catch (err) {
            next(err);
          }
        });
      }
    } catch (error) {
      next(error);
    }
  };
}
