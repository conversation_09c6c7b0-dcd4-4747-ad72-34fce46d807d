import chalk from 'chalk';
import { consola } from 'consola';

import type { Request, Response, NextFunction } from 'express';

export function logger(req: Request, _res: Response, next: NextFunction): void {
  const ip: string = req.ip || 'Unknown IP';
  const method: string = req.method;
  const url: string = req.url;
  const version: string = req.httpVersion;
  const userAgent: string = req.headers['user-agent'] || 'Unknown User-Agent';

  // Define color mappings for HTTP methods using chalk
  const methodColors: Record<string, (msg: string) => string> = {
    GET: chalk.green,
    POST: chalk.blue,
    PUT: chalk.yellow,
    DELETE: chalk.red,
    PATCH: chalk.cyan,
    OPTIONS: chalk.magenta,
    DEFAULT: chalk.gray
  };

  // Get the corresponding color function or use default
  const colorize = (methodColors[method] || methodColors.DEFAULT)!;

  // Construct the formatted log message
  const message: string = `${chalk.gray(ip)} [${colorize(method)}] ${chalk.cyan(url)} HTTP/${chalk.yellow(version)} ${chalk.dim(userAgent)}`;

  // Log using consola
  consola.info(message);

  next();
}
