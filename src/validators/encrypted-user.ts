import { z } from 'zod';

// Response schema
export const userResponseSchema = z.object({
  id: z.number(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  createdAt: z.string(),
  updatedAt: z.string()
});

// Create user schema
export const createUserSchema = {
  request: {
    body: z.object({
      email: z.string().email('Invalid email format'),
      firstName: z.string().min(1, 'First name is required'),
      lastName: z.string().min(1, 'Last name is required')
    })
  },
  response: {
    body: z.object({
      success: z.boolean(),
      data: userResponseSchema
    })
  }
} as const;

// Get user schema
export const getUserSchema = {
  request: {
    params: z.object({
      id: z.string().regex(/^\d+$/, 'ID must be a number')
    })
  },
  response: {
    body: z.object({
      success: z.boolean(),
      data: userResponseSchema
    })
  }
} as const;

// Update user schema
export const updateUserSchema = {
  request: {
    params: z.object({
      id: z.string().regex(/^\d+$/, 'ID must be a number')
    }),
    body: z.object({
      email: z.string().email('Invalid email format').optional(),
      firstName: z.string().min(1, 'First name is required').optional(),
      lastName: z.string().min(1, 'Last name is required').optional()
    })
  },
  response: {
    body: z.object({
      success: z.boolean(),
      data: userResponseSchema
    })
  }
} as const;

// Delete user schema
export const deleteUserSchema = {
  request: {
    params: z.object({
      id: z.string().regex(/^\d+$/, 'ID must be a number')
    })
  }
} as const;

// Schema types
export type CreateUserSchema = typeof createUserSchema;
export type GetUserSchema = typeof getUserSchema;
export type UpdateUserSchema = typeof updateUserSchema;
export type DeleteUserSchema = typeof deleteUserSchema;
