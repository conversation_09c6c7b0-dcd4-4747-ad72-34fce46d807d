import { eq, sql } from 'drizzle-orm';

import { db } from '../db';
import { encryptedUsers } from '../db/schemas/encrypted-users';

import type EncryptedUser from '../@types/encrypted-user';

export class EncryptedUserService {
  // Create new user with encrypted fields
  async createUser(
    dto: EncryptedUser.CreateDTO
  ): Promise<EncryptedUser.DecryptedUser> {
    const [user] = await db
      .insert(encryptedUsers)
      .values({
        email: dto.email,
        firstName: sql`PGP_SYM_ENCRYPT(${dto.firstName}, ${process.env.PG_ENCRYPTION_KEY})`,
        lastName: sql`PGP_SYM_ENCRYPT(${dto.lastName}, ${process.env.PG_ENCRYPTION_KEY})`
      })
      .returning({
        id: encryptedUsers.id,
        email: encryptedUsers.email,
        firstName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.firstName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        lastName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.lastName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        createdAt: encryptedUsers.createdAt,
        updatedAt: encryptedUsers.updatedAt
      });

    if (!user) {
      throw new Error('Failed to create user');
    }

    return user as EncryptedUser.DecryptedUser;
  }

  // Get all users with decrypted fields
  async getAllUsers(): Promise<EncryptedUser.DecryptedUser[]> {
    const users = await db
      .select({
        id: encryptedUsers.id,
        email: encryptedUsers.email,
        firstName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.firstName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        lastName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.lastName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        createdAt: encryptedUsers.createdAt,
        updatedAt: encryptedUsers.updatedAt
      })
      .from(encryptedUsers);

    return users as EncryptedUser.DecryptedUser[];
  }

  // Get user by ID with decrypted fields
  async getUserById(id: number): Promise<EncryptedUser.DecryptedUser | null> {
    const [user] = await db
      .select({
        id: encryptedUsers.id,
        email: encryptedUsers.email,
        firstName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.firstName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        lastName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.lastName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        createdAt: encryptedUsers.createdAt,
        updatedAt: encryptedUsers.updatedAt
      })
      .from(encryptedUsers)
      .where(eq(encryptedUsers.id, id));

    return user as EncryptedUser.DecryptedUser | null;
  }

  // Update user with encrypted fields
  async updateUser(
    id: number,
    dto: EncryptedUser.UpdateDTO
  ): Promise<EncryptedUser.DecryptedUser | null> {
    const updates: Record<string, unknown> = {};

    if (dto.email) {
      updates.email = dto.email;
    }

    if (dto.firstName) {
      updates.firstName = sql`PGP_SYM_ENCRYPT(${dto.firstName}, ${process.env.PG_ENCRYPTION_KEY})`;
    }

    if (dto.lastName) {
      updates.lastName = sql`PGP_SYM_ENCRYPT(${dto.lastName}, ${process.env.PG_ENCRYPTION_KEY})`;
    }

    const [user] = await db
      .update(encryptedUsers)
      .set(updates)
      .where(eq(encryptedUsers.id, id))
      .returning({
        id: encryptedUsers.id,
        email: encryptedUsers.email,
        firstName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.firstName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        lastName: sql<string>`PGP_SYM_DECRYPT(${encryptedUsers.lastName}::bytea, ${process.env.PG_ENCRYPTION_KEY})`,
        createdAt: encryptedUsers.createdAt,
        updatedAt: encryptedUsers.updatedAt
      });

    return user as EncryptedUser.DecryptedUser | null;
  }

  // Delete user
  async deleteUser(id: number): Promise<void> {
    await db.delete(encryptedUsers).where(eq(encryptedUsers.id, id));
  }
}
