import argon2 from 'argon2';
import jwt from 'jsonwebtoken';

import {
  createUser,
  updateUser,
  getUserByEmail,
  updateUserPassword
} from './user-services';
import { BackendError } from '../utils/errors';

import type Auth from '../@types/auth';

/**
 * Auth Service
 * Handles authentication, registration, and user management
 */
export class AuthService implements Auth.AuthServiceInterface {
  private readonly jwtSecret: string;
  private readonly jwtExpiresIn: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
  }

  /**
   * User login
   */
  async login(dto: Auth.LoginDTO): Promise<Auth.AuthResponse> {
    const { email, password } = dto;

    // Get user by email
    const user = await getUserByEmail(email);
    if (!user) {
      throw new BackendError('INVALID_CREDENTIALS', {
        message: 'Invalid email or password',
        context: 'body',
        statusCode: 401
      });
    }

    // Check if user is deleted
    if (user.deleted) {
      throw new BackendError('ACCOUNT_DISABLED', {
        message: 'Account has been disabled',
        context: 'body',
        statusCode: 401
      });
    }

    // Verify password
    if (!user.password) {
      throw new BackendError('INVALID_CREDENTIALS', {
        message: 'Invalid email or password',
        context: 'body',
        statusCode: 401
      });
    }

    const isValidPassword = await this.verifyPassword(password, user.password);
    if (!isValidPassword) {
      throw new BackendError('INVALID_CREDENTIALS', {
        message: 'Invalid email or password',
        context: 'body',
        statusCode: 401
      });
    }

    // Generate token
    const token = this.generateToken(user);

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    return {
      token,
      user: userWithoutPassword
    };
  }

  /**
   * User registration
   */
  async register(dto: Auth.RegisterDTO): Promise<Auth.RegisterResponse> {
    const { email, password, firstName, lastName, phone } = dto;

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      throw new BackendError('CONFLICT', {
        message: 'User with this email already exists',
        context: 'body',
        statusCode: 409
      });
    }

    // Hash password
    const hashedPassword = await this.hashPassword(password);

    // Generate OTP
    const otp = this.generateOtp();

    // Create user
    const user = await createUser({
      email,
      password: hashedPassword,
      firstName,
      lastName,
      phone
    });

    // Set OTP for email verification
    await updateUser(user.userId, { otp });

    // TODO: Send OTP via email
    console.log(`OTP for ${email}: ${otp}`);

    return {
      message:
        'Account created successfully. Please verify your email with the OTP sent.',
      userId: user.userId,
      requiresVerification: true
    };
  }

  /**
   * Verify OTP and activate account
   */
  async verifyOtp(dto: Auth.VerifyOtpDTO): Promise<Auth.AuthResponse> {
    const { email, otp } = dto;

    // Get user by email
    const user = await getUserByEmail(email);
    if (!user) {
      throw new BackendError('NOT_FOUND', {
        message: 'User not found',
        context: 'body',
        statusCode: 404
      });
    }

    // Check OTP
    if (!user.otp || user.otp !== otp) {
      throw new BackendError('INVALID_OTP', {
        message: 'Invalid or expired OTP',
        context: 'body',
        statusCode: 400
      });
    }

    // Update user as verified and clear OTP
    const updatedUser = await updateUser(user.userId, {
      emailVerified: true,
      status: 'OFFLINE',
      otp: null
    });

    if (!updatedUser) {
      throw new BackendError('INTERNAL_ERROR', {
        message: 'Failed to verify account',
        context: 'body',
        statusCode: 500
      });
    }

    // Generate token
    const token = this.generateToken(updatedUser);

    // Remove password from response
    const { password: _, ...userWithoutPassword } = updatedUser;

    return {
      token,
      user: userWithoutPassword
    };
  }

  /**
   * Send OTP for password reset
   */
  async sendOtp(email: string): Promise<Auth.OtpResponse> {
    // Get user by email
    const user = await getUserByEmail(email);
    if (!user) {
      throw new BackendError('NOT_FOUND', {
        message: 'User not found',
        context: 'body',
        statusCode: 404
      });
    }

    // Generate OTP
    const otp = this.generateOtp();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Update user with OTP
    await updateUser(user.userId, { otp });

    // TODO: Send OTP via email
    console.log(`Password reset OTP for ${email}: ${otp}`);

    return {
      message: 'OTP sent to your email address',
      expiresAt
    };
  }

  /**
   * Reset password with OTP
   */
  async resetPassword(
    dto: Auth.ResetPasswordDTO
  ): Promise<{ message: string }> {
    const { email, otp, newPassword } = dto;

    // Get user by email
    const user = await getUserByEmail(email);
    if (!user) {
      throw new BackendError('NOT_FOUND', {
        message: 'User not found',
        context: 'body',
        statusCode: 404
      });
    }

    // Check OTP
    if (!user.otp || user.otp !== otp) {
      throw new BackendError('INVALID_OTP', {
        message: 'Invalid or expired OTP',
        context: 'body',
        statusCode: 400
      });
    }

    // Hash new password
    const hashedPassword = await this.hashPassword(newPassword);

    // Update password and clear OTP
    await updateUserPassword(user.userId, hashedPassword);
    await updateUser(user.userId, { otp: null });

    return {
      message: 'Password reset successfully'
    };
  }

  /**
   * Generate JWT token
   */
  generateToken(user: Auth.User): string {
    const payload: Auth.JwtPayload = {
      userId: user.userId,
      email: user.email,
      role: user.role || 'USER'
    };

    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.jwtExpiresIn
    } as jwt.SignOptions);
  }

  /**
   * Verify JWT token
   */
  verifyToken(token: string): Auth.JwtPayload {
    try {
      return jwt.verify(token, this.jwtSecret) as Auth.JwtPayload;
    } catch {
      throw new BackendError('INVALID_TOKEN', {
        message: 'Invalid or expired token',
        context: 'body',
        statusCode: 401
      });
    }
  }

  /**
   * Hash password
   */
  async hashPassword(password: string): Promise<string> {
    return argon2.hash(password);
  }

  /**
   * Verify password
   */
  async verifyPassword(
    password: string,
    hashedPassword: string
  ): Promise<boolean> {
    try {
      return await argon2.verify(hashedPassword, password);
    } catch {
      return false;
    }
  }

  /**
   * Generate 6-digit OTP
   */
  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}

// Export singleton instance
export const authService = new AuthService();
