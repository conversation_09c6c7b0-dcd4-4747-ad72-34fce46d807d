import { eq, and } from 'drizzle-orm';

import { db } from '../db';
import { users } from '../db/schemas/users';

import type Auth from '../@types/auth';

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<Auth.User | null> {
  const [user] = await db
    .select()
    .from(users)
    .where(and(eq(users.email, email), eq(users.deleted, false)))
    .limit(1);

  return (user as Auth.User) || null;
}

/**
 * Get user by ID
 */
export async function getUserById(id: number): Promise<Auth.User | null> {
  const [user] = await db
    .select()
    .from(users)
    .where(and(eq(users.userId, id), eq(users.deleted, false)))
    .limit(1);

  return (user as Auth.User) || null;
}

/**
 * Create new user
 */
export async function createUser(userData: {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: Auth.User['role'];
}): Promise<Auth.User> {
  const [user] = await db
    .insert(users)
    .values({
      email: userData.email,
      password: userData.password,
      firstName: userData.firstName,
      lastName: userData.lastName,
      phone: userData.phone,
      role: userData.role || 'USER',
      status: 'ACTIVATION_PENDING',
      emailVerified: false,
      deleted: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
    .returning();

  if (!user) {
    throw new Error('Failed to create user');
  }

  return user as Auth.User;
}

/**
 * Update user
 */
export async function updateUser(
  id: number,
  updates: Partial<{
    firstName: string | null;
    lastName: string | null;
    phone: string | null;
    emailVerified: boolean;
    status: Auth.User['status'];
    otp: string | null;
  }>
): Promise<Auth.User | null> {
  const [user] = await db
    .update(users)
    .set({
      ...updates,
      updatedAt: new Date().toISOString()
    })
    .where(and(eq(users.userId, id), eq(users.deleted, false)))
    .returning();

  return (user as Auth.User) || null;
}

/**
 * Update user password
 */
export async function updateUserPassword(
  id: number,
  hashedPassword: string
): Promise<boolean> {
  const result = await db
    .update(users)
    .set({
      password: hashedPassword,
      updatedAt: new Date().toISOString()
    })
    .where(and(eq(users.userId, id), eq(users.deleted, false)));

  return result.length > 0;
}
