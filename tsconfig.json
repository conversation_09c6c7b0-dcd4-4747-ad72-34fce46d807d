{
  "compilerOptions": {
    "target": "es2022",
    /* If you're using React: */
    "jsx": "react-jsx",
    /* If your code doesn't run in the DOM: */
    "lib": ["es2022"],
    "moduleDetection": "force",
    "module": "ESNext",
    /* If NOT transpiling with TypeScript: */
    "moduleResolution": "Bundler",
    /* Aliases */
    "paths": {
      "@/*": ["./src/*"]
    },
    "resolveJsonModule": true,
    "allowJs": true,
    /* Strictness */
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "noEmit": true,
    /* Base Options: */
    "esModuleInterop": true,
    "verbatimModuleSyntax": true,
    "skipLibCheck": true
  }
}
