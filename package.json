{"name": "ola-backend-api-service", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "engines": {"node": ">=20.11.0"}, "scripts": {"build": "esbuild src/server.ts --bundle --outfile=build/server.js --platform=node --format=esm --packages=external", "start": "node build/server.js", "dev": "tsx watch src/server.ts", "generate": "drizzle-kit generate", "migrate": "tsx src/db/migrate.ts", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.744.0", "@mailchimp/mailchimp_transactional": "^1.0.59", "@types/bcryptjs": "^2.4.6", "@types/mailchimp__mailchimp_transactional": "^1.0.10", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/multer-s3": "^3.0.3", "@types/qs": "^6.9.18", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.14", "argon2": "^0.41.1", "bcryptjs": "^2.4.3", "chalk": "^5.4.1", "consola": "^3.4.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-perfectionist": "^4.13.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-promise": "^7.2.1", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "morgan": "^1.10.0", "multer": "1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemon": "^3.1.9", "postgres": "^3.4.5", "qs": "^6.14.0", "request-ip": "^3.3.0", "ts-node": "^10.9.2", "twilio": "^5.4.3", "uuid": "^11.0.5", "winston": "^3.17.0", "ws": "^8.18.0", "zod": "^3.24.1"}, "devDependencies": {"@antfu/eslint-config": "^4.2.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/compat": "^1.2.6", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.20.0", "@stylistic/eslint-plugin": "^3.1.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.8", "@types/node": "^22.13.1", "@types/pg": "^8.11.11", "@types/request-ip": "^0.0.41", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "eslint": "^9.20.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "^15.4.3", "pg": "^8.13.1", "prettier": "^3.5.0", "tsx": "^4.19.2", "typescript": "^5.7.3"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "*.ts": ["eslint --fix", "prettier --write"], "*.tsx": ["eslint --fix", "prettier --write"]}}