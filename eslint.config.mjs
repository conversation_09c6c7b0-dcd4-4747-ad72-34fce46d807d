/* eslint-disable node/no-unpublished-import */
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import { fixupConfigRules, fixupPluginRules } from '@eslint/compat';
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import typescriptEslint from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import _import from 'eslint-plugin-import';
import node from 'eslint-plugin-node';
import perfectionist from 'eslint-plugin-perfectionist'
import promise from 'eslint-plugin-promise';
import unusedImports from 'eslint-plugin-unused-imports';


const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all
});

export default [
  {
    ignores: [
      '**/jest.config.js',
      '**/coverage',
      '**/build',
      '**/dist',
      '**/*.css',
      '**/*.svg',
      '**/*.spec.ts',
      '**/*.test.ts',
      '**/jest.config.js',
      '**/drizzle.config.ts'
    ]
  },
  ...fixupConfigRules(
    compat.extends(
      'eslint:recommended',
      'plugin:@typescript-eslint/recommended',
      'plugin:import/errors',
      'plugin:import/warnings',
      'plugin:import/typescript',
      'plugin:node/recommended',
      'plugin:promise/recommended'
    )
  ),
  {
    plugins: {
      '@typescript-eslint': fixupPluginRules(typescriptEslint),
      import: fixupPluginRules(_import),
      node: fixupPluginRules(node),
      promise: fixupPluginRules(promise),
      'unused-imports': fixupPluginRules(unusedImports),
      perfectionist: fixupPluginRules(perfectionist)
    },

    languageOptions: {
      parser: tsParser,
      ecmaVersion: 2021,
      sourceType: 'module',

      parserOptions: {
        project: './tsconfig.json'
      }
    },

    rules: {
      'no-console': 'off',
      'no-process-exit': 'off',
      'import/no-unresolved': 'off',
      'node/no-missing-import': 'off',
      '@typescript-eslint/no-redeclare': 'off',
      '@typescript-eslint/no-this-alias': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-empty-interface': 'off',
      'node/no-unsupported-features/es-syntax': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      'node/no-unsupported-features/node-builtins': 'off',
      'node/no-unsupported-features/es-builtins': 'off',
      'comma-dangle': 'off',

      // Handle unused imports
      '@typescript-eslint/no-unused-vars': ['error', { 'vars': 'all', 'args': 'after-used', 'ignoreRestSiblings': true, 'varsIgnorePattern': '^_', 'argsIgnorePattern': '^_' }],
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        { 'vars': 'all', 'varsIgnorePattern': '^_', 'args': 'after-used', 'argsIgnorePattern': '^_' }
      ],

      // Import sorting
      'import/order': [
        'error',
        {
          groups: [
            'builtin',
            'external',
            'internal',
            ['parent', 'sibling'],
            'index',
            'object',
            'type',
          ],
          'newlines-between': 'always',
          alphabetize: {
            order: 'asc',
            caseInsensitive: true,
          },
        },
      ],
      'perfectionist/sort-named-imports': [
        'warn',
        {
          order: 'asc',
          type: 'line-length',
        },
      ],
      'perfectionist/sort-named-exports': [
        'warn',
        {
          order: 'asc',
          type: 'line-length',
        },
      ],
      'perfectionist/sort-exports': [
        'warn',
        {
          order: 'asc',
          type: 'line-length',
        },
      ],
    }
  }
];
